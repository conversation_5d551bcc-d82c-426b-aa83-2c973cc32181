#!/bin/bash

# Jenkins Setup Script for case-loop project
echo "Starting <PERSON> setup script"

# Make script exit on any error
set -e

# Display Node and npm versions
echo "Node version: $(node -v)"
echo "NPM version: $(npm -v)"

# Clear npm cache first
echo "Clearing npm cache"
npm cache clean --force

# Install dependencies in root directory
echo "Installing root dependencies"
npm install

# Install dependencies in case-loop directory
echo "Installing case-loop dependencies"
cd case-loop
npm install

# Verify that critical Vue CLI dependencies are installed
echo "Verifying Vue CLI dependencies"
if [ ! -d "node_modules/@vue/cli-plugin-babel" ] || \
   [ ! -d "node_modules/@vue/cli-plugin-eslint" ] || \
   [ ! -d "node_modules/@vue/cli-service" ]; then
   echo "Installing missing Vue CLI dependencies"
   npm install @vue/cli-plugin-babel @vue/cli-plugin-eslint @vue/cli-service --save-dev
fi

# Run a test lint to verify everything is working
echo "Testing lint command"
./node_modules/.bin/vue-cli-service lint --no-fix

echo "Jenkins setup completed successfully" 