<template>
  <div class="session-favorite">
    <!-- 收藏会话列表 -->
    <a-card :bordered="false">
      <template #title>
        <div class="table-header">
          <div class="table-title">收藏的会话</div>
          <div class="table-actions">
            <a-space>
              <a-button
                  :disabled="!hasFavoriteSelectedRows"
                  @click="handleRemoveFavoriteClick"
                  danger
                  class="custom-button"
              >
                <template #icon>
                  <DeleteOutlined/>
                </template>
                移除收藏
              </a-button>
              <a-button
                  :disabled="!hasFavoriteSelectedRows"
                  @click="handleExportFavoriteClick"
                  class="custom-button"
              >
                <template #icon>
                  <ExportOutlined/>
                </template>
                导出选中
              </a-button>
            </a-space>
          </div>
        </div>
      </template>

      <a-table
          :columns="favoriteColumns"
          :data-source="favoriteTableData"
          :loading="favoriteLoading"
          :pagination="favoritePagination"
          :row-selection="{ selectedRowKeys: favoriteSelectedRowKeys, onChange: onFavoriteSelectChange }"
          :row-key="record => record.sessionId"
          @change="handleFavoriteTableChange"
      >
        <!-- 会话ID列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'sessionId'">
            <a @click="viewSessionDetail(record)">{{ record.sessionId }}</a>
          </template>

          <!-- 空间/应用列 -->
          <template v-if="column.key === 'workspaceApp'">
            <div v-if="record.workspaceAppInfo && record.workspaceAppInfo.length > 0">
              <a-tag color="blue" v-for="ws in record.workspaceAppInfo" :key="ws.workspaceId">
                {{ ws.workspaceName }}
              </a-tag>
              <a-tag color="green" v-for="app in record.workspaceAppInfo.flatMap(ws => ws.applicationList)"
                     :key="app.applicationId">
                {{ app.applicationName }}
              </a-tag>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 场景列 -->
          <template v-if="column.dataIndex === 'sceneName'">
            <a-tag v-if="record.sceneName" :color="getScenarioColor(record.sceneName)">
              {{ record.sceneName }}
            </a-tag>
            <span v-else>-</span>
          </template>

          <!-- 转人工列 -->
          <template v-if="column.dataIndex === 'transferStaff'">
            <template v-if="record.transferStaff !== null && record.transferStaff !== undefined">
              <a-tag :color="getTransferStaffTagColor(record.transferStaff)">
                {{ getTransferStaffTagText(record.transferStaff) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 解决情况列 -->
          <template v-if="column.dataIndex === 'sessionSolved'">
            <template v-if="record.sessionSolved !== null && record.sessionSolved !== undefined">
              <a-tag :color="getSolvedTagColor(record.sessionSolved)">
                {{ getSolvedTagText(record.sessionSolved) }}
              </a-tag>
            </template>
            <span v-else>-</span>
          </template>

          <!-- 满意度列 -->
          <template v-if="column.dataIndex === 'stars'">
            <div v-if="record.stars !== null && record.stars !== undefined">
              <a-rate :value="record.stars" disabled :count="5"/>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 收藏时间列 -->
          <template v-if="column.dataIndex === 'favoriteTime'">
            {{ record.favoriteTime ? new Date(record.favoriteTime).toLocaleString() : '-' }}
          </template>

          <!-- 操作列 -->
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a @click="viewSessionDetail(record)">查看</a>
              <a @click="removeSingleFavorite(record)" style="color: #ff4d4f;">移除收藏</a>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import {defineComponent, ref, reactive, computed, onMounted} from 'vue';
import {message} from 'ant-design-vue';
import {
  ExportOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import {useRouter} from 'vue-router';

// 导入API
import {
  getFavoriteSessionList,
  unfavoriteSession
} from '@/api/analysis';

export default defineComponent({
  name: 'SessionFavorite',
  components: {
    ExportOutlined,
    DeleteOutlined
  },
  setup() {
    const router = useRouter();
    
    // 收藏会话相关状态
    const favoriteLoading = ref(false);
    const favoriteTableData = ref([]);
    const favoriteSelectedRowKeys = ref([]);
    const hasFavoriteSelectedRows = computed(() => favoriteSelectedRowKeys.value.length > 0);

    // 收藏会话分页配置
    const favoritePagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 收藏会话表格列配置
    const favoriteColumns = [
      {
        title: '会话ID',
        dataIndex: 'sessionId',
        key: 'sessionId',
        width: 200,
        fixed: 'left'
      },
      {
        title: '创建时间',
        dataIndex: 'time',
        key: 'time',
        width: 170,
        sorter: true,
        customRender: ({text}) => {
          return text ? new Date(text).toLocaleString() : '-';
        }
      },
      {
        title: '空间/应用',
        key: 'workspaceApp',
        width: 150,
        customRender: ({record}) => {
          if (record.workspaceAppInfo && record.workspaceAppInfo.length > 0) {
            const workspaceNames = record.workspaceAppInfo.map(ws => ws.workspaceName);
            const appNames = record.workspaceAppInfo.flatMap(ws =>
                ws.applicationList.map(app => app.applicationName)
            );
            return `${workspaceNames.join(', ')}/${appNames.join(', ')}`;
          }
          return '-';
        }
      },
      {
        title: '场景',
        dataIndex: 'sceneName',
        key: 'sceneName',
        width: 150
      },
      {
        title: '转人工',
        dataIndex: 'transferStaff',
        key: 'transferStaff',
        width: 100
      },
      {
        title: '是否解决',
        dataIndex: 'sessionSolved',
        key: 'sessionSolved',
        width: 100
      },
      {
        title: '满意度',
        dataIndex: 'stars',
        key: 'stars',
        width: 150
      },
      {
        title: '收藏时间',
        dataIndex: 'favoriteTime',
        key: 'favoriteTime',
        width: 170,
        sorter: true
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        width: 150,
        fixed: 'right'
      }
    ];

    // 初始化
    onMounted(() => {
      fetchFavoriteData();
    });
