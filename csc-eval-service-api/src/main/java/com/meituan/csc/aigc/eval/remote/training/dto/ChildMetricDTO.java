package com.meituan.csc.aigc.eval.remote.training.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-04-18 14:16
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChildMetricDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long metricId;
    /**
     * 评测标准
     */
    private String standard;
    /**
     * 是否选择
     */
    private Boolean selected = Boolean.FALSE;
    private ManualAnnotationDTO childManualAnnotation;
}
