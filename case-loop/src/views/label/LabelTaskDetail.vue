<template>
  <div class="label-task-detail">
    <div class="page-header">
      <h2>打标任务详情</h2>
      <div class="task-info">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="任务名称">客服对话质量评估-5月</a-descriptions-item>
          <a-descriptions-item label="任务ID">TK2023050001</a-descriptions-item>
          <a-descriptions-item label="创建时间">2023-05-10 10:30:00</a-descriptions-item>
          <a-descriptions-item label="状态">进行中</a-descriptions-item>
          <a-descriptions-item label="完成率">65% (130/200)</a-descriptions-item>
          <a-descriptions-item label="负责人">张三</a-descriptions-item>
        </a-descriptions>
      </div>
    </div>

    <div class="operation-bar">
      <a-space>
        <a-input-search
          v-model:value="searchValue"
          placeholder="搜索任务ID或关键词"
          style="width: 300px"
          @search="onSearch"
        />
        <a-select
          v-model:value="filterStatus"
          style="width: 120px"
          placeholder="筛选状态"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="待打标">待打标</a-select-option>
          <a-select-option value="已打标">已打标</a-select-option>
          <a-select-option value="已审核">已审核</a-select-option>
        </a-select>
        <a-button type="primary">批量导出</a-button>
      </a-space>
    </div>

    <a-table 
      :dataSource="caseList" 
      :columns="columns" 
      :pagination="pagination"
      :loading="loading"
      @change="handleTableChange"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ record.status }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="primary" size="small" @click="handleLabel(record)">去打标</a-button>
            <a-button size="small" @click="handleAnalysis(record)">case分析</a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, reactive } from 'vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'LabelTaskDetail',
  setup() {
    const searchValue = ref('');
    const filterStatus = ref('');
    const loading = ref(false);
    const caseList = ref([]);
    
    // 模拟数据
    const mockData = [];
    for (let i = 1; i <= 50; i++) {
      mockData.push({
        id: `case-${i}`,
        taskId: `TASK-${1000 + i}`,
        customerName: `用户${i}`,
        serviceAgent: `客服${i % 5 + 1}`,
        createTime: `2023-05-${10 + i % 20} ${10 + i % 12}:${i % 60}:00`,
        status: i % 3 === 0 ? '待打标' : (i % 3 === 1 ? '已打标' : '已审核'),
        duration: `${i % 10 + 1}分钟`,
        score: i % 3 === 1 ? (85 + i % 15) : null,
      });
    }

    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: mockData.length,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条记录`,
    });

    const columns = [
      {
        title: '任务ID',
        dataIndex: 'taskId',
        key: 'taskId',
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        key: 'createTime',
        sorter: true,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        filters: [
          { text: '待打标', value: '待打标' },
          { text: '已打标', value: '已打标' },
          { text: '已审核', value: '已审核' },
        ],
      },
      {
        title: '时长',
        dataIndex: 'duration',
        key: 'duration',
      },
      {
        title: '评分',
        dataIndex: 'score',
        key: 'score',
        customRender: ({ text }) => text ? `${text}分` : '-',
      },
      {
        title: '操作',
        key: 'action',
      },
    ];

    // 初始化加载数据
    const loadData = () => {
      loading.value = true;
      // 模拟 API 请求
      setTimeout(() => {
        const { current, pageSize } = pagination;
        const start = (current - 1) * pageSize;
        const end = current * pageSize;
        
        let filteredData = [...mockData];
        
        // 搜索过滤
        if (searchValue.value) {
          filteredData = filteredData.filter(item => 
            item.taskId.includes(searchValue.value) || 
            item.customerName.includes(searchValue.value)
          );
        }
        
        // 状态过滤
        if (filterStatus.value) {
          filteredData = filteredData.filter(item => item.status === filterStatus.value);
        }
        
        pagination.total = filteredData.length;
        caseList.value = filteredData.slice(start, end);
        loading.value = false;
      }, 500);
    };

    onMounted(() => {
      loadData();
    });

    // 处理表格变化
    const handleTableChange = (pag, filters, sorter) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      
      // 处理排序
      if (sorter.field) {
        // 这里可以根据 sorter 参数处理排序逻辑
      }
      
      loadData();
    };

    // 处理搜索
    const onSearch = () => {
      pagination.current = 1;
      loadData();
    };

    // 获取状态标签颜色
    const getStatusColor = (status) => {
      switch (status) {
        case '待打标': return 'blue';
        case '已打标': return 'green';
        case '已审核': return 'purple';
        default: return 'default';
      }
    };

    // 打标操作
    const handleLabel = (record) => {
      message.info(`跳转到打标页面，任务ID: ${record.taskId}`);
      // 可以在这里实现页面跳转
    };

    // case分析操作
    const handleAnalysis = (record) => {
      message.info(`跳转到case分析页面，任务ID: ${record.taskId}`);
      // 可以在这里实现页面跳转
    };

    return {
      searchValue,
      filterStatus,
      loading,
      caseList,
      columns,
      pagination,
      onSearch,
      handleTableChange,
      getStatusColor,
      handleLabel,
      handleAnalysis,
    };
  },
});
</script>

<style scoped>
.label-task-detail {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.task-info {
  margin-top: 16px;
}

.operation-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}
</style> 