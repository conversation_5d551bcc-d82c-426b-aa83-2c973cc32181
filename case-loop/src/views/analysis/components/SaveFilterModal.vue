<template>
  <div class="save-filter-modal">
    <a-modal
      v-model:visible="modalVisible"
      title="保存筛选条件"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="名称" required>
          <a-input
            v-model:value="filterName"
            placeholder="请输入筛选条件名称"
            :maxlength="20"
            show-count
            @pressEnter="handleOk"
          />
        </a-form-item>

        <a-alert
          type="info"
          show-icon
          style="margin-bottom: 16px"
        >
          <template #message>
            将保存当前所有筛选条件，包括已选择的筛选项和填写的内容
          </template>
        </a-alert>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'SaveFilterModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    filterData: {
      type: Object,
      required: true
    }
  },
  emits: ['update:visible', 'save'],
  setup(props, { emit }) {
    // 模态框可见性
    const modalVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    });

    // 筛选名称
    const filterName = ref('');

    // 监听模态框显示状态，重置名称
    watch(() => props.visible, (visible) => {
      if (visible) {
        filterName.value = '';
      }
    });

    // 处理确认
    const handleOk = () => {
      if (!filterName.value.trim()) {
        message.error('请输入筛选条件名称');
        return;
      }

      emit('save', filterName.value.trim());
      filterName.value = '';
      modalVisible.value = false;
    };

    // 处理取消
    const handleCancel = () => {
      filterName.value = '';
      modalVisible.value = false;
    };

    return {
      modalVisible,
      filterName,
      handleOk,
      handleCancel
    };
  }
});
</script>

<style scoped>
/* 保存筛选条件模态框样式 */
</style>