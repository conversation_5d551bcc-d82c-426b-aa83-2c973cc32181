<template>
  <div class="app-version-select">
    <a-select
      v-model:value="selectedValue"
      :placeholder="placeholder"
      :loading="loading"
      :disabled="disabled"
      @change="handleChange"
      allow-clear
    >
      <a-select-option v-for="option in options" :key="option.value" :value="option.value">
        {{ option.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'AppVersionSelect',
  props: {
    value: {
      type: [String, Number, null],
      default: null
    },
    formData: {
      type: Object,
      required: true
    },
    sceneRelationConfig: {
      type: Object,
      required: true
    },
    isApplyingSavedFilter: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择应用版本'
    }
  },
  emits: ['update:value', 'change'],
  setup(props, { emit }) {
    const loading = ref(false);
    const options = ref([]);
    const selectedValue = ref(null);
    
    // 是否禁用
    const disabled = computed(() => {
      // 如果没有选择空间和应用，则禁用
      return !props.formData.workspaceAndApp || props.formData.workspaceAndApp.length < 2;
    });
    
    // 获取应用版本选项
    const fetchVersionOptions = async () => {
      if (disabled.value && !props.isApplyingSavedFilter) {
        options.value = [];
        return;
      }
      
      // 如果是应用保存的筛选条件，不清空选项
      if (props.isApplyingSavedFilter) {
        return;
      }
      
      const workspaceAndApp = props.formData.workspaceAndApp;
      if (!workspaceAndApp || workspaceAndApp.length < 2) {
        options.value = [];
        return;
      }
      
      loading.value = true;
      try {
        console.log('AppVersionSelect组件接收到的配置:', props.sceneRelationConfig);
        
        // 适配原始应用API返回的数据结构
        // 原始应用返回格式: { sceneInfos: [{ sceneId, sceneName, workspaceAppList }] }
        const config = props.sceneRelationConfig;
        if (!config || !config.sceneInfos) {
          options.value = [];
          return;
        }
        
        // 合并所有场景中的应用版本，优先从应用版本列表获取
        // 在原始应用API中，应用版本通常是单独请求的，这里我们可以模拟一个默认版本
        // 实际使用时可能需要调用另一个API获取应用版本列表
        options.value = [
          {
            value: 'latest',
            label: '最新版本'
          }
        ];
      } catch (error) {
        console.error('获取应用版本失败', error);
        message.error('获取应用版本失败');
        options.value = [];
      } finally {
        loading.value = false;
      }
    };
    
    // 监听工作空间和应用变化
    watch(() => props.formData.workspaceAndApp, () => {
      // 清空已选值
      if (!props.isApplyingSavedFilter) {
        selectedValue.value = null;
        emit('update:value', null);
        emit('change', null);
      }
      
      fetchVersionOptions();
    }, { deep: true });
    
    // 监听场景关系配置变化
    watch(() => props.sceneRelationConfig, () => {
      fetchVersionOptions();
    }, { deep: true });
    
    // 处理外部值变化
    watch(() => props.value, (newVal) => {
      if (newVal !== selectedValue.value) {
        selectedValue.value = newVal;
      }
    });
    
    // 处理值变化
    const handleChange = (value) => {
      emit('update:value', value);
      emit('change', value);
    };
    
    return {
      loading,
      options,
      selectedValue,
      disabled,
      handleChange
    };
  }
});
</script>

<style scoped>
.app-version-select {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}
</style> 