<template>
  <div class="filter-condition-modal">
    <a-modal
      v-model:visible="modalVisible"
      title="自定义筛选项"
      width="700px"
      :destroyOnClose="true"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-alert
        type="info"
        show-icon
        style="margin-bottom: 16px"
      >
        <template #message>
          请选择你需要的筛选项，已选条件将显示在筛选区域
        </template>
      </a-alert>
      
      <div class="search-bar" style="margin-bottom: 16px">
        <a-input-search
          v-model:value="searchText"
          placeholder="搜索筛选项"
          style="width: 100%"
          allow-clear
        />
      </div>
      
      <div class="filter-list">
        <a-checkbox-group v-model:value="selectedKeys" class="checkbox-group">
          <div v-for="filter in filteredFilters" :key="filter.key" class="filter-item">
            <a-checkbox :value="filter.key">{{ filter.label }}</a-checkbox>
          </div>
        </a-checkbox-group>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue';

export default defineComponent({
  name: 'FilterConditionModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    availableFilters: {
      type: Array,
      default: () => []
    },
    selectedFilters: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:visible', 'confirm'],
  setup(props, { emit }) {
    // 模态框可见性
    const modalVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    });
    
    // 搜索文本
    const searchText = ref('');
    
    // 过滤后的筛选项列表
    const filteredFilters = computed(() => {
      if (!searchText.value) return props.availableFilters;
      
      return props.availableFilters.filter(filter => 
        filter.label.toLowerCase().includes(searchText.value.toLowerCase())
      );
    });
    
    // 选中的筛选项键
    const selectedKeys = ref([]);
    
    // 监听模态框显示状态变化
    watch(() => props.visible, (visible) => {
      if (visible) {
        // 当模态框打开时，同步已选择的筛选项
        selectedKeys.value = props.selectedFilters.map(filter => filter.key);
        // 清空搜索文本
        searchText.value = '';
      }
    });
    
    // 监听外部选中的筛选项变化
    watch(() => props.selectedFilters, (newVal) => {
      if (props.visible) {
        selectedKeys.value = newVal.map(filter => filter.key);
      }
    }, { immediate: true });
    
    // 处理确认
    const handleOk = () => {
      // 构建选中的筛选项对象
      const selected = selectedKeys.value.map(key => {
        const filter = props.availableFilters.find(f => f.key === key);
        if (filter) {
          // 保留原有筛选项的属性，并标记为已选中
          return {
            ...filter,
            selected: true
          };
        }
        return null;
      }).filter(Boolean);
      
      emit('confirm', selected);
      modalVisible.value = false;
    };
    
    // 处理取消
    const handleCancel = () => {
      // 恢复之前的选中状态
      selectedKeys.value = props.selectedFilters.map(filter => filter.key);
      modalVisible.value = false;
    };
    
    return {
      modalVisible,
      searchText,
      selectedKeys,
      filteredFilters,
      handleOk,
      handleCancel
    };
  }
});
</script>

<style scoped>
.filter-list {
  max-height: 350px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.checkbox-group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.filter-item {
  width: 33.33%;
  margin-bottom: 12px;
  padding-right: 16px;
  box-sizing: border-box;
}

@media screen and (max-width: 768px) {
  .filter-item {
    width: 50%;
  }
}

@media screen and (max-width: 576px) {
  .filter-item {
    width: 100%;
  }
}
</style> 