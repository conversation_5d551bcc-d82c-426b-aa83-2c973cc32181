"use strict";
// for convenience's sake - export the types directly from here so consumers
// don't need to reference/install both packages in their code
Object.defineProperty(exports, "__esModule", { value: true });
exports.TSESTree = exports.AST_TOKEN_TYPES = exports.AST_NODE_TYPES = void 0;
var types_1 = require("@typescript-eslint/types");
Object.defineProperty(exports, "AST_NODE_TYPES", { enumerable: true, get: function () { return types_1.AST_NODE_TYPES; } });
Object.defineProperty(exports, "AST_TOKEN_TYPES", { enumerable: true, get: function () { return types_1.AST_TOKEN_TYPES; } });
Object.defineProperty(exports, "TSESTree", { enumerable: true, get: function () { return types_1.TSESTree; } });
