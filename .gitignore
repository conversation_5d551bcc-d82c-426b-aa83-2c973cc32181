# 忽略 node_modules 目录下的所有文件
node_modules/
build/

# 忽略日志文件
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 忽略环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 忽略构建输出目录
/dist
/build

# 忽略依赖锁定文件（可选，取决于您的项目需求）
# package-lock.json
# yarn.lock

# 忽略编辑器和操作系统生成的文件
.DS_Store
.idea/
.vscode/
*.swp
*.swo


node_modules
dist
build
dist-ssr
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
