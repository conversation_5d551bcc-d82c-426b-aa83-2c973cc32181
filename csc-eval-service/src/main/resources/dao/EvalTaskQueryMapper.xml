<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.EvalTaskQueryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.EvalTaskQueryPo">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="dataset_id" property="datasetId"/>
        <result column="session_id" property="sessionId"/>
        <result column="conversation_id" property="conversationId"/>
        <result column="content" property="content"/>
        <result column="input" property="input"/>
        <result column="expect" property="expect"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="turn" property="turn"/>
        <result column="params" property="params"/>
        <result column="status" property="status"/>
        <result column="inspectors" property="inspectors"/>
        <result column="summary" property="summary"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, dataset_id, session_id, conversation_id, content, input, expect, gmt_created, gmt_modified, turn,
        params, status, inspectors, summary
    </sql>

    <!-- 查询不包含大字段的结果 -->
    <sql id="No_Blob_Column_List">
        id, task_id, dataset_id, session_id, conversation_id, gmt_created, gmt_modified, turn, status, inspectors
    </sql>

    <select id="getNoBlobByTaskIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="No_Blob_Column_List"/>
        FROM eval_task_query
        WHERE task_id IN
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>
    <select id="getQueryCount" resultType="com.meituan.csc.aigc.eval.dto.task.TaskDetailCountDTO">
        SELECT
        task_id,
        COUNT(*) as totalCount,
        SUM(status IN
        <foreach collection="completeStatus" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>) as completedCount
        FROM
        eval_task_query
        WHERE 1=1
        <if test="taskIdList != null and taskIdList.size() > 0">
            AND
            task_id IN
            <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        GROUP BY
        task_id;
    </select>

</mapper>
