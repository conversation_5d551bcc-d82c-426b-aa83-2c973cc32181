<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.FileRecordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.FileRecordsPo">
        <id column="id" property="id" />
        <result column="file_name" property="fileName" />
        <result column="file_size" property="fileSize" />
        <result column="file_rows" property="fileRows" />
        <result column="original_file_url" property="originalFileUrl" />
        <result column="result_file_url" property="resultFileUrl" />
        <result column="business_scene" property="businessScene" />
        <result column="task_status" property="taskStatus" />
        <result column="mis_id" property="misId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="error_info" property="errorInfo" />
        <result column="tag_group_id" property="tagGroupId" />
        <result column="tag_group_name" property="tagGroupName" />
        <result column="user_name" property="userName" />
        <result column="scene_id" property="sceneId" />
        <result column="task_type" property="taskType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_name, file_size, file_rows, original_file_url, result_file_url, business_scene, task_status, mis_id, create_time, update_time, error_info, tag_group_id, tag_group_name, user_name, scene_id, task_type
    </sql>

</mapper>
