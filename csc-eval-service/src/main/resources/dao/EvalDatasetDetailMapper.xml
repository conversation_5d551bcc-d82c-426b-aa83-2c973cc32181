<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meituan.csc.aigc.eval.dao.mapper.EvalDatasetDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meituan.csc.aigc.eval.dao.entity.EvalDatasetDetailPo">
        <id column="id" property="id" />
        <result column="dataset_id" property="datasetId" />
        <result column="content" property="content" />
        <result column="params" property="params" />
        <result column="input" property="input" />
        <result column="expect" property="expect" />
        <result column="output" property="output" />
        <result column="session_id" property="sessionId" />
        <result column="turn" property="turn" />
        <result column="status" property="status" />
        <result column="gmt_created" property="gmtCreated" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="creator_mis" property="creatorMis" />
        <result column="updater_mis" property="updaterMis" />
        <result column="summary" property="summary" />
        <result column="version_id" property="versionId" />
        <result column="llm_message_id" property="llmMessageId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dataset_id, content, params, input, expect, output, session_id, turn, status, gmt_created, gmt_modified, creator_mis, updater_mis, summary, version_id, llm_message_id
    </sql>

</mapper>
