package com.meituan.csc.aigc.eval.dao.common;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.dianping.zebra.dao.mybatis.ZebraMapperScannerConfigurer;
import com.dianping.zebra.group.jdbc.GroupDataSource;
import org.apache.ibatis.session.LocalCacheScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR>
 */
@Configuration
public class DataSourceConfiguration {

    /**
     * mapperScan扫描路径
     */
    private static final String BASE_PACKAGE = "com.meituan.csc.aigc.eval.dao.mapper";
    /**
     * factoryName
     */
    private static final String SQL_SESSION_FACTORY_NAME = "zebraSqlSessionFactory";

    @Bean(name = "dataSource")
    @Primary
    public GroupDataSource zebraDataSource() throws IOException {
        Resource resource = new ClassPathResource("/zebra.properties");
        Properties props = PropertiesLoaderUtils.loadProperties(resource);
        GroupDataSource dataSource = new GroupDataSource(props.getProperty("my.zebra.jdbcRef"));
        dataSource.setPoolType("druid");
        // 添加 rewriteBatchedStatements=true 参数
        dataSource.setExtraJdbcUrlParams("useSSL=false&rewriteBatchedStatements=true");
        dataSource.init();
        return dataSource;
    }

    /**
     * 添加分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    @Bean(name = SQL_SESSION_FACTORY_NAME)
    public MybatisSqlSessionFactoryBean sqlSessionFactory(DataSource dataSource, PaginationInterceptor paginationInterceptor) throws IOException {
        MybatisSqlSessionFactoryBean ssfb = new MybatisSqlSessionFactoryBean();
        ssfb.setDataSource(dataSource);
        // mapper xml扫描路径
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        ssfb.setMapperLocations(resolver.getResources("classpath*:dao/*.xml"));
        MybatisConfiguration mybatisConfiguration = new MybatisConfiguration();
        // 开启驼峰映射
        mybatisConfiguration.setMapUnderscoreToCamelCase(true);
        // 开启插件
        mybatisConfiguration.addInterceptor(paginationInterceptor);
        // 每次执行SQL语句后清空Mybatis的一级缓存
        mybatisConfiguration.setLocalCacheScope(LocalCacheScope.STATEMENT);
        ssfb.setConfiguration(mybatisConfiguration);
        return ssfb;
    }

    /**
     * 配置扫描 Mapper 路径
     */
    @Bean
    public ZebraMapperScannerConfigurer mapperScannerConfigurer() {
        ZebraMapperScannerConfigurer configurer = new ZebraMapperScannerConfigurer();
        configurer.setSqlSessionFactoryBeanName(SQL_SESSION_FACTORY_NAME);
        configurer.setBasePackage(BASE_PACKAGE);
        return configurer;
    }
}