package com.meituan.csc.aigc.eval.dto.ivr;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 基础信息DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseInfoDTO implements Serializable {

    private static final long serialVersionUID = 6695321018810710596L;

    /**
     * 热线号码
     */
    private String hotlineNumber;

    /**
     * 进线时间
     */
    private Date callTime;

    /**
     * 通话ID
     */
    private String callId;

    /**
     * 主叫号码
     */
    private String ani;

    /**
     * 落地号码
     */
    private String dnis;

    /**
     * 业务用户ID
     */
    private String userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 是否转人工
     */
    private Boolean isTransferStaff;

    /**
     * 是否灰度
     */
    private Boolean isGray;

    /**
     * 首个技能组
     */
    private String firstSkill;

    /**
     * 全部技能组列表
     */
    private List<String> fullSkills;

    /**
     * 工单ID列表
     */
    private List<String> caseIds;

    /**
     * 工单关联问题
     */
    private String caseAllQuestion;

    /**
     * 是否存在中继转出
     */
    private Boolean hasTransferCall;

    /**
     * 美团用户ID
     */
    private String mtUserId;

    /**
     * 满意度
     */
    private String satisfaction;

    /**
     * 智能满意度
     */
    private String intelligentSatisfaction;

    /**
     * 转人工结果状态
     */
    private String manualResult;

    /**
     * 通话业务归属
     */
    private String buName;
}
