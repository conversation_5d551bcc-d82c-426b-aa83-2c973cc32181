package com.meituan.csc.aigc.eval.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description 自研大模型http请求返回结果
 * <AUTHOR>
 * @since 2023-07-12
 */
@Data
public class SelfDevelopGPTHttpResponse implements Serializable {

    private Integer status;

    private String message;

    private RestTemplateData data;

    @Data
    public static class RestTemplateData implements Serializable{
        private String result;

        private String resultRole;

        private List<Choice> choices;

        private Usage usage;
    }


    @Data
    public static class Choice implements Serializable{
        private Message message;

        private Integer index;

        private String finishReason;
    }

    @Data
    public static class Message implements Serializable{
        private String content;

        private String role;
    }

    @Data
    public static class Usage implements Serializable{
        private Long completion_tokens;

        private Long prompt_tokens;

        private Long total_tokens;
    }

}
