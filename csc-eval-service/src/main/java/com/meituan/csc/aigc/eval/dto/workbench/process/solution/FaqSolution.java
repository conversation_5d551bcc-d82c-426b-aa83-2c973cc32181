package com.meituan.csc.aigc.eval.dto.workbench.process.solution;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.csc.aigc.eval.dto.workbench.process.ButtonClickDTO;
import com.meituan.csc.aigc.eval.dto.workbench.process.RobotSolution;
import com.meituan.csc.aigc.eval.dto.workbench.process.enums.RobotSolutionType;
import lombok.Data;

import java.util.List;

/**
 * Faq解决方案
 */
@Data
public class FaqSolution implements RobotSolution {
    @JsonProperty("faqId")
    private Integer faqId;

    @JsonProperty("faqName")
    private String faqName;

    @JsonProperty("buttonClickList")
    private List<ButtonClickDTO> buttonClickList;

    @JsonProperty("taskKey")
    private String taskKey;

    @JsonProperty("taskName")
    private String taskName;

    @JsonProperty("taskType")
    private Integer taskType = 0;

    @JsonProperty("taskVersion")
    private String taskVersion;

    @JsonProperty("taskInstanceId")
    private String taskInstanceId;

    @JsonProperty("taskTag")
    private String taskTag;

    /**
     * 获取解决方案类型
     *
     * @return
     */
    @Override
    public String getSolutionType() {
        return RobotSolutionType.FaqSolution.getCode();
    }

    /**
     * 获取解决方案名称
     *
     * @return
     */
    @Override
    public String getSolutionName() {
        return RobotSolutionType.FaqSolution.getName();
    }
}

