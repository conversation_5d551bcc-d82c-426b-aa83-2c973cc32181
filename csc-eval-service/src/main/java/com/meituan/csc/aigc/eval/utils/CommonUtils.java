package com.meituan.csc.aigc.eval.utils;

import com.meituan.csc.aigc.eval.exception.CheckException;
import com.meituan.csc.aigc.eval.exception.EvalException;

import java.util.Random;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class CommonUtils {

    // 随机数生成器
    private static final Random RANDOM = new Random();

    public static String uuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成Long类型的唯一ID
     * 注意：虽然碰撞概率低，但不能保证完全不重复
     * @return Long类型的唯一ID
     */
    public static Long longUuid() {
        // 使用当前时间戳作为基础
        long timestamp = System.currentTimeMillis();
        // 添加一个随机数来降低冲突概率
        long random = RANDOM.nextLong() & 0xfffffffL; // 使用最后28位
        // 移位组合时间戳和随机数
        return (timestamp << 28) | random;
    }

    /**
     * 如果表达式为false，则抛出异常
     * @param express
     * @param message
     * @throws EvalException
     */
    public static void checkEval(boolean express, String message) throws EvalException {
        if (!express) {
            throw new CheckException(message);
        }
    }
}
