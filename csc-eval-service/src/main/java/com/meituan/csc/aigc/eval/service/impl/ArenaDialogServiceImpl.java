package com.meituan.csc.aigc.eval.service.impl;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.config.RhinoThreadPoolConfig;
import com.meituan.csc.aigc.eval.constants.CatConstants;
import com.meituan.csc.aigc.eval.dao.entity.EvalArenaDataPo;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalArenaDataGeneratorService;
import com.meituan.csc.aigc.eval.dto.ArenaSessionExcelData;
import com.meituan.csc.aigc.eval.enums.ArenaVoteResultEnum;
import com.meituan.csc.aigc.eval.service.ArenaDialogueService;
import com.meituan.csc.aigc.eval.service.push.DxPushTextService;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ArenaDialogServiceImpl implements ArenaDialogueService {

    private static final String S3_CHAT_NAME_FORMAT = "arenaSession_%s_%s.xlsx";
    private static final String DOWNLOD_MSG = "您的竞技对话下载任务已完成，也可点击[此处|%s]进行下载";

    @Autowired
    EvalArenaDataGeneratorService arenaDataGeneratorService;
    @Autowired
    private ExcelService excelService;

    @Autowired
    private DxPushTextService dxPushTextService;

    @Override
    public String uploadArenaSessionData(String mis, List<EvalArenaDataPo> evalArenaDataPos) {
        List<ArenaSessionExcelData> excelDataList = convertToExcelData(evalArenaDataPos);
        return uploadDataToS3(mis, excelDataList);
    }

    private List<ArenaSessionExcelData> convertToExcelData(List<EvalArenaDataPo> evalArenaDataPos) {
        List<ArenaSessionExcelData> excelDataList = Lists.newArrayList();
        for (EvalArenaDataPo arenaDataPo : evalArenaDataPos) {
            ArenaSessionExcelData excelData = new ArenaSessionExcelData();
            excelData.setSessionId(arenaDataPo.getSessionId());
            excelData.setDialogHistory(arenaDataPo.getContent());
            excelData.setModelName(arenaDataPo.getPlayer());
            excelData.setVoteStatus(ArenaVoteResultEnum.parse(arenaDataPo.getStatus()).getInfo());
            excelData.setCreator(arenaDataPo.getCreatorMis());
            excelData.setCreateTime(arenaDataPo.getGmtCreated());
            excelDataList.add(excelData);
        }
        return excelDataList;
    }

    /**
     * 上传数据到S3
     * @param mis
     * @param excelDataList
     * @return
     */
    private String uploadDataToS3(String mis, List<ArenaSessionExcelData> excelDataList) {
        try {
            String path = String.format(S3_CHAT_NAME_FORMAT, mis, new Date());


            String uploadUrl = excelService.generateSingletonHeadExcelAndUpload(excelDataList, ArenaSessionExcelData.class, path, path,"xlsx", mis);
            pushDownloadLinkToUser(uploadUrl);
            return uploadUrl;
        } catch (Exception e) {
            log.error("EVAL,uploadDataToS3 error:{}", e.getMessage(), e);
            Cat.logErrorWithCategory(CatConstants.S3_UPLOAD_FILE_ERROR, e);
        }
        return null;
    }

    private void pushDownloadLinkToUser(String uploadUrl) {
        final User user = UserUtils.getUser();
        RhinoThreadPoolConfig.submmit(() -> {
            try {
                dxPushTextService.pushTextByMisName(String.format(DOWNLOD_MSG, uploadUrl), user.getLogin());
            } catch (Exception e) {
                Cat.logErrorWithCategory("竞技对话下载文件推送异常", e);
            }
        });
    }
}
