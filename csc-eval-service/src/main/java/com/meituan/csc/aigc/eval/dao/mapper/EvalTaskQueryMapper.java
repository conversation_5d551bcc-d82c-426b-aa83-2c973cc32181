package com.meituan.csc.aigc.eval.dao.mapper;

import com.meituan.csc.aigc.eval.dao.entity.EvalTaskQueryPo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meituan.csc.aigc.eval.dto.task.TaskDetailCountDTO;

import java.util.List;

/**
 * <p>
 * 评测任务-query维度表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface EvalTaskQueryMapper extends BaseMapper<EvalTaskQueryPo> {

    List<EvalTaskQueryPo> getNoBlobByTaskIdList(List<Long> taskIdList);

    List<TaskDetailCountDTO> getQueryCount(List<Long> taskIdList, List<Integer> completeStatus);
}
