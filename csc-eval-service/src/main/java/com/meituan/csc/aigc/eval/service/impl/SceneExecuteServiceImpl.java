package com.meituan.csc.aigc.eval.service.impl;

import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dto.scene.SceneDTO;
import com.meituan.csc.aigc.eval.service.SceneExecuteService;
import com.meituan.inf.xmdlog.ConfigUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SceneExecuteServiceImpl implements SceneExecuteService {
    @Override
    public List<SceneDTO> getSceneList() {
        return Lion.getList(ConfigUtil.getAppkey(), LionConstants.SCENE_LIST, SceneDTO.class);
    }
}
