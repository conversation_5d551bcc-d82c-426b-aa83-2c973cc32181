package com.meituan.csc.aigc.eval.dao.service.generator.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meituan.csc.aigc.eval.dao.entity.AutoInspectConfigPo;
import com.meituan.csc.aigc.eval.dao.mapper.AutoInspectConfigMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.AutoInspectConfigGeneratorService;
import com.meituan.csc.aigc.eval.enums.AutoInspectConfigStatusEnum;
import com.meituan.csc.aigc.eval.param.autoInspect.AutoInspectConfigConditionParam;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AutoInspectConfigGeneratorServiceImpl extends ServiceImpl<AutoInspectConfigMapper, AutoInspectConfigPo> implements AutoInspectConfigGeneratorService {
    @Override
    public List<AutoInspectConfigPo> getByCondition(AutoInspectConfigConditionParam condition) {
        CommonUtils.checkEval(condition.getFrequencyType() == null || condition.getFrequencyType() != 1 || condition.getDayOfWeek() != null, "每周执行的任务必须指定执行日期");
        LambdaQueryWrapper<AutoInspectConfigPo> queryWrapper = new LambdaQueryWrapper<>();
        // 根据执行类型过滤
        Optional.ofNullable(condition.getMode()).ifPresent(mode -> queryWrapper.eq(AutoInspectConfigPo::getMode, mode));
        // 根据巡检是否启用过滤
        Optional.ofNullable(condition.getStatus()).ifPresent(status -> queryWrapper.eq(AutoInspectConfigPo::getStatus, status));
        if (condition.getFrequencyType() != null) {
            if (condition.getFrequencyType() == 0) {
                queryWrapper.eq(AutoInspectConfigPo::getFrequencyType, 0);
            } else {
                queryWrapper.and(wrapper -> wrapper.eq(AutoInspectConfigPo::getFrequencyType, 1)
                        .eq(AutoInspectConfigPo::getFrequencyDay, condition.getDayOfWeek()));
            }
        } else if (condition.getDayOfWeek() != null) {
            queryWrapper.and(child -> child.eq(AutoInspectConfigPo::getFrequencyType, 0).or().eq(AutoInspectConfigPo::getFrequencyType, 1).and(wrapper -> wrapper.eq(AutoInspectConfigPo::getFrequencyDay, condition.getDayOfWeek())));
        }
        // 根据modelConfigVersionId过滤
        Optional.ofNullable(condition.getModelConfigVersionId()).ifPresent(modelConfigVersionId -> queryWrapper.eq(AutoInspectConfigPo::getModelConfigVersionId, modelConfigVersionId));
        // 根据applicationId过滤
        Optional.ofNullable(condition.getApplicationId()).ifPresent(applicationId -> queryWrapper.eq(AutoInspectConfigPo::getPlatformApp, applicationId));
        // 根据metricId过滤
        Optional.ofNullable(condition.getMetricId()).ifPresent(metricId -> queryWrapper.eq(AutoInspectConfigPo::getMetricId, metricId));
        return this.list(queryWrapper);
    }

    @Override
    public List<AutoInspectConfigPo> getCurrentValidConfig(String applicationId,String workspaceId) {
        LambdaQueryWrapper<AutoInspectConfigPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(AutoInspectConfigPo::getPlatformApp, applicationId)
                .eq(AutoInspectConfigPo::getPlatformWorkspace, workspaceId)
                .and(wrapper ->
                        wrapper.eq(AutoInspectConfigPo::getStatus, AutoInspectConfigStatusEnum.AVAILABLE.getCode())
                                .or()
                                .eq(AutoInspectConfigPo::getStatus, AutoInspectConfigStatusEnum.UN_TURN.getCode()))
                .orderByAsc(AutoInspectConfigPo::getGmtCreated);
        return this.list(queryWrapper);
    }

    @Override
    public List<AutoInspectConfigPo> getByIdList(List<Long> configIds) {
        LambdaQueryWrapper<AutoInspectConfigPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AutoInspectConfigPo::getId, configIds);
        return this.list(queryWrapper);
    }
}

