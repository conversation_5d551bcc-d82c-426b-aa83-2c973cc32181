package com.meituan.csc.aigc.eval.dao.service.generator.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meituan.csc.aigc.eval.dao.entity.SessionAnalysisPo;
import com.meituan.csc.aigc.eval.dao.mapper.SessionAnalysisMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.SessionAnalysisGeneratorService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 会话分析结果表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@Service
public class SessionAnalysisGeneratorServiceImpl extends ServiceImpl<SessionAnalysisMapper, SessionAnalysisPo> implements SessionAnalysisGeneratorService {

    @Override
    public List<SessionAnalysisPo> getListByTaskId(Long taskId) {
        if (Objects.isNull(taskId)) {
            // 参数为空
            return null;
        }
        // 这里需要修改一下，同一个sessionId需要拿到createTime时间最新的结果
        List<SessionAnalysisPo> poList = this.lambdaQuery().eq(SessionAnalysisPo::getTaskId, taskId)
                .orderByDesc(SessionAnalysisPo::getCreateTime).list();
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }

        // 根据sessionId分组，每组只保留createTime最新的记录
        return poList.stream()
                .collect(Collectors.groupingBy(SessionAnalysisPo::getSessionId))
                .values().stream()
                .map(list -> list.get(0)) // 由于已按createTime降序排序，每组的第一条即为最新记录
                .collect(Collectors.toList());
    }

    @Override
    public List<SessionAnalysisPo> getListByFileKeyAndTags(Long taskId, String questionTags) {
        if (Objects.isNull(taskId) || StringUtils.isBlank(questionTags)) {
            // 参数为空
            return null;
        }
        List<SessionAnalysisPo> poList = this.lambdaQuery().eq(SessionAnalysisPo::getTaskId, taskId)
                .in(SessionAnalysisPo::getQuestionTags, questionTags)
                .list();
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        return poList;
    }

    @Override
    public List<SessionAnalysisPo> getListByFileKeyAndTagsPage(Long taskId, String problemTags, Integer pageNo, Integer pageSize) {
        if (Objects.isNull(taskId) || StringUtils.isBlank(problemTags)) {
            return new ArrayList<>();
        }
        if (Objects.isNull(pageNo) || Objects.isNull(pageSize)) {
            pageNo = 1;
            pageSize = 10;
        }

        Page<SessionAnalysisPo> page = new Page<>(pageNo, pageSize);
        // 用problemTags进行模糊匹配
        IPage<SessionAnalysisPo> poPage = this.lambdaQuery().eq(SessionAnalysisPo::getTaskId, taskId)
                .like(SessionAnalysisPo::getQuestionTags, problemTags)
                .page(page);
        if (Objects.isNull(poPage) || CollectionUtils.isEmpty(poPage.getRecords())) {
            return new ArrayList<>();
        }
        return poPage.getRecords();
    }

    @Override
    public Integer getCountByFileKeyAndTags(Long taskId, String problemTags) {
        if (Objects.isNull(taskId) || StringUtils.isBlank(problemTags)) {
            return null;
        }
        Integer count = this.lambdaQuery().eq(SessionAnalysisPo::getTaskId, taskId)
                .in(SessionAnalysisPo::getQuestionTags, problemTags)
                .count();
        if (Objects.isNull(count)) {
            return null;
        }
        return count;
    }

    @Override
    public List<SessionAnalysisPo> getSessionAnalysisBySubTaskIdAndSessionId(Long subTaskId, String sessionId) {
        if (Objects.isNull(subTaskId) || StringUtils.isBlank(sessionId)) {
            return null;
        }
        List<SessionAnalysisPo> poList = this.lambdaQuery().eq(SessionAnalysisPo::getTaskId, subTaskId)
                .eq(SessionAnalysisPo::getSessionId, sessionId).list();
        if (CollectionUtils.isEmpty(poList)) {
            return null;
        }
        return poList;
    }
}
