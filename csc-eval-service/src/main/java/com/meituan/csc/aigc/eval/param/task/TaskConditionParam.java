package com.meituan.csc.aigc.eval.param.task;

import com.meituan.csc.aigc.eval.param.dataset.CommonConditionParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskConditionParam extends CommonConditionParam implements Serializable {
    private Long taskId;
    private Long datasetId;
    private Integer abilityType;
    private String modelType;
    private String creatorMis;
    private String name;
    private String nameLike;
    private Integer status;
    //任务类型，0-人工评测 1-自动评测 2-竞技评测 3-定期巡检
    private Integer taskType;

    private List<Integer> taskTypes;
    private Integer callType;
    private String inspectorMis;
    private String scene;
    private Integer platformType;
    private Long applicationId;
    private Long metricId;
    //新增workspace查询条件
    private String platformWorkspace;
    private String aidaApplicationId;
    private Date startTime;
}
