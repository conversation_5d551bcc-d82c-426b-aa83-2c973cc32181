package com.meituan.csc.aigc.eval.dto.workbench.process.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.csc.aigc.eval.dto.workbench.process.ServiceProcessDetail;
import lombok.Data;

/**
 * 操作信息详情
 */
@Data
public class OperationDetail implements ServiceProcessDetail {

    @JsonProperty("selfServiceToolId")
    private String selfServiceToolId;

    @JsonProperty("selfServiceToolName")
    private String selfServiceToolName;

    @JsonProperty("serviceProcessType")
    private String serviceProcessType;

    @JsonProperty("content")
    private String content;
}

