package com.meituan.csc.aigc.eval.param.dataset;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DatasetDetailConditionParam extends CommonConditionParam implements Serializable {
    private String sessionId;
    private Long datasetId;
    private String llmMessageId;
    private Long versionId;
}
