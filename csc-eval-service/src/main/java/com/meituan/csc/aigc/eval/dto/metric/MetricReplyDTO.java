package com.meituan.csc.aigc.eval.dto.metric;

import com.meituan.csc.aigc.eval.param.gpt.TokenParam;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MetricReplyDTO implements Serializable {
    private String result;
    private String message;
    private TokenParam tokenParam;
    private Long executeTime;
    private String content;
    /**
     * 是否结束评测，人工指标为false，自动指标为true
     */
    private Boolean isEnd;
    private Boolean isSuccess;
}
