package com.meituan.csc.aigc.eval.service;

import com.meituan.csc.aigc.eval.dao.entity.EvalDatasetDetailPo;
import com.meituan.csc.aigc.eval.dao.entity.EvalDatasetPo;
import com.meituan.csc.aigc.eval.param.dataset.DataSetParam;
import com.meituan.csc.aigc.eval.param.dataset.DatasetPullResponse;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DatasetPullService {

    /**
     * 拉取数据
     *
     * @param param     参数
     * @param datasetId 数据集ID
     */
    DatasetPullResponse pullData(DataSetParam param, Long datasetId);

    /**
     * 构建数据集详情
     *
     * @param createMis 创建人
     * @param date      日期
     * @param dataList  数据集详情
     * @param datasetId 数据集ID
     * @return 数据集详情
     */
    List<EvalDatasetDetailPo> buildDatasetDetail(String createMis, Date date, List<Map<String, String>> dataList, Long datasetId, Long versionId, EvalDatasetPo evalDataset);
}
