package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class AutoInspectionStatisticsDTO implements Serializable {
    /**
     * 指标唯一id
     */
    private Long metricId;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 总检查次数
     */
    private Long totalInspections;

    /**
     * 采纳次数
     */
    private Long adoptedCount;

    /**
     * 采纳率
     */
    private BigDecimal adoptionRate;

}
