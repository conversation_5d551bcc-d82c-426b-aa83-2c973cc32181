package com.meituan.csc.aigc.eval.helper;

import com.sankuai.csccratos.aida.config.client.dto.eval.MessagesDTO;

import java.util.List;

public class ConversationThreadLocalHelper {
    public static final InheritableThreadLocal<List<MessagesDTO>> CACHE = new InheritableThreadLocal<>();

    public static void setCache(List<MessagesDTO> messagesList) {
        CACHE.set(messagesList);
    }

    public static List<MessagesDTO> getCache() {
        return CACHE.get();
    }

    public static void removeCache() {
        CACHE.remove();
    }
}
