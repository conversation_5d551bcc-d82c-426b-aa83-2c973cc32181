package com.meituan.csc.aigc.eval.proxy;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.meituan.talos.commons.exception.TalosException;
import com.sankuai.data.talos.AsyncTalosClient;
import com.sankuai.data.talos.QueryRequest;
import com.sankuai.data.talos.Talos;
import com.sankuai.data.talos.model.Engine;
import com.sankuai.data.talos.model.QueryResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HiveRequestServiceProxy {

    public AsyncTalosClient getClient() throws TalosException {
        String userName = Lion.getString(ConfigUtil.getAppkey(), "talos.username");
        String password = Lion.getString(ConfigUtil.getAppkey(), "talos.password");
        AsyncTalosClient talosClient = new AsyncTalosClient(userName, password);
        talosClient.openSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
        return talosClient;
    }

    public QueryResult invoke(AsyncTalosClient client, String sql) throws TalosException {
        QueryRequest request = new QueryRequest.Builder()
                .engine(Engine.Onesql)
                .statement(sql)
                .build();
        String qid = client.submit(request);
        int res = client.waitForFinished(qid, 600, true);
        if (res == 1) {
            return client.getQueryResult(qid);
        }
        if (res == 0) {
            // 超时后终止任务
            client.cancel(qid);
        }
        CommonUtils.checkEval(res != 0, "查询hive超过10分钟，任务终止");
        CommonUtils.checkEval(res != -1, "查询hive失败");
        return null;
    }

    public void closeClient(AsyncTalosClient talosClient) {
        try {
            boolean isSuccess = talosClient.closeSession(new Talos.RequestOption(60, 60, 60, TimeUnit.SECONDS));
            CommonUtils.checkEval(isSuccess, "关闭talos session失败");
        } catch (Exception e) {
            Cat.logError(e);
            log.error("关闭talos session失败,msg={}", e.getMessage(), e);
        }
    }
}
