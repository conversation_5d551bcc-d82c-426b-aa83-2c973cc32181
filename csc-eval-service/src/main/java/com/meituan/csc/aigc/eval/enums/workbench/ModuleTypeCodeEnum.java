package com.meituan.csc.aigc.eval.enums.workbench;

import lombok.Getter;
import lombok.AllArgsConstructor;

/**
 * IVR模块类型枚举
 */
@Getter
@AllArgsConstructor
public enum ModuleTypeCodeEnum {
    ENTRY(1, "Start", "入口模块", "入口"),
    WORK_TIME(2, "Time", "工作时间模块", "工作时间"),
    MENU(3, "Menu", "菜单模块", "菜单导航"),
    VOICE(4, "Voice", "标准语音模块", "语音播报"),
    JUMP(5, "Jump", "跳转模块", "跳转"),
    SERVICE(6, "Judge", "服务接口模块", "判断"),
    INTELLIGENT_ROUTE(7, "Route", "智能路由模块", "智能路由"),
    RECOMMEND_BUSSINESS(8, "Bussiness", "业务推荐模块", "业务推荐"),
    RECOMMEND_TARGET(9, "Target", "目标推荐模块", "目标推荐"),
    RECOMMEND_FAQ(10, "Faq", "faq推荐模块", "FAQ推荐"),
    INPUT(11, "input", "输入模块", "按键输入"),
    TEMPORARY(12, "Temporary", "爆线引流模块", "爆线引流"),
    SPLIT_FLOW(13, "Split", "分流模块", "分流"),
    VOICE_MACHINE(14, "Machine", "语音机器人模块", "语音机器人"),
    END(15, "End", "结束模块", "结束"),
    CALL_OUT(16, "CallOut", "外呼模块", "外呼"),
    ALGORITHM_BU_REC(17, "AlgorithmBuRec", "算法业务推荐模块", "算法业务推荐"),
    DYNAMIC_BROADCAST(18, "DynamicBroadcast", "动态播报模块", "动态播报"),
    FLOW_TRANSFER(19, "FlowTransfer", "流程调用模块", "流程调用"),
    OUTPUT(20, "Output", "输出模块", "输出"),
    SOLUTION_RECOMMEND(21, "SolutionRecommend", "解决方案模块", "解决方案"),
    VUI(22, "VUI", "智能语音导航", "智能语音导航"),
    LLM(23, "LLM", "大模型模块", "大模型");

    /**
     * 编码
     */
    private final Integer code;
    
    /**
     * 英文名称
     */
    private final String name;
    
    /**
     * 模块名称
     */
    private final String moduleName;
    
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码获取枚举
     */
    public static ModuleTypeCodeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ModuleTypeCodeEnum moduleType : ModuleTypeCodeEnum.values()) {
            if (moduleType.getCode().equals(code)) {
                return moduleType;
            }
        }
        return null;
    }

    /**
     * 根据英文名称获取枚举
     */
    public static ModuleTypeCodeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        for (ModuleTypeCodeEnum moduleType : ModuleTypeCodeEnum.values()) {
            if (moduleType.getName().equals(name)) {
                return moduleType;
            }
        }
        return null;
    }

}
