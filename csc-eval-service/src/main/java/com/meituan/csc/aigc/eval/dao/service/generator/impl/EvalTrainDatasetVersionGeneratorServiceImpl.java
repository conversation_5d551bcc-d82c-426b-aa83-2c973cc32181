package com.meituan.csc.aigc.eval.dao.service.generator.impl;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meituan.csc.aigc.eval.dao.entity.EvalTrainDatasetVersionPo;
import com.meituan.csc.aigc.eval.dao.mapper.EvalTrainDatasetVersionMapper;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalTrainDatasetVersionGeneratorService;
import com.meituan.csc.aigc.eval.enums.trainDataset.TrainDatasetStatusEnum;
import com.meituan.csc.aigc.eval.param.trainDataset.TrainDatasetVersionConditionParam;
import com.meituan.inf.xmdlog.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 存放训练集的版本历史信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-29
 */
@Service
public class EvalTrainDatasetVersionGeneratorServiceImpl extends ServiceImpl<EvalTrainDatasetVersionMapper, EvalTrainDatasetVersionPo> implements EvalTrainDatasetVersionGeneratorService {

    @Override
    public List<EvalTrainDatasetVersionPo> getByIdList(List<Long> trainDatasetVersionIds) {
        LambdaQueryChainWrapper<EvalTrainDatasetVersionPo> queryWrapper = lambdaQuery();
        queryWrapper.in(EvalTrainDatasetVersionPo::getId, trainDatasetVersionIds)
                .ne(EvalTrainDatasetVersionPo::getStatus, TrainDatasetStatusEnum.Deleted.getCode());
        return queryWrapper.list();
    }

    @Override
    public List<EvalTrainDatasetVersionPo> getByCondition(TrainDatasetVersionConditionParam param) {
        LambdaQueryChainWrapper<EvalTrainDatasetVersionPo> queryWrapper = lambdaQuery();
        // 应用条件
        Optional.ofNullable(param.getVersionIds()).ifPresent(versionIds -> queryWrapper.in(EvalTrainDatasetVersionPo::getId, versionIds));
        Optional.ofNullable(param.getVersionId()).ifPresent(versionId -> queryWrapper.eq(EvalTrainDatasetVersionPo::getId, versionId));
        Optional.ofNullable(param.getDatasetId()).ifPresent(datasetId -> queryWrapper.eq(EvalTrainDatasetVersionPo::getDatasetId, datasetId));
        Optional.ofNullable(param.getTaskIds()).ifPresent(taskId -> queryWrapper.in(EvalTrainDatasetVersionPo::getTaskId, taskId));
        if(CollectionUtils.isNotEmpty(param.getTaskIds())){
            queryWrapper.in(EvalTrainDatasetVersionPo::getTaskId, param.getTaskIds());
        }
        if (param.getStartTime() != null && param.getEndTime() != null) {
            queryWrapper.between(EvalTrainDatasetVersionPo::getCreatedAt, param.getStartTime(), param.getEndTime());
        }
        if (!StringUtils.isEmpty(param.getUpdateMis()) ) {
            queryWrapper.eq(EvalTrainDatasetVersionPo::getUpdaterMis, param.getUpdateMis());
        }
        Optional.ofNullable(param.getCreationMethod()).ifPresent(creationMethod -> queryWrapper.eq(EvalTrainDatasetVersionPo::getCreateMethod, creationMethod));
        Optional.ofNullable(param.getStatus()).ifPresent(status -> queryWrapper.eq(EvalTrainDatasetVersionPo::getStatus, status));
        Optional.ofNullable(param.getNameLike()).ifPresent(nameLike -> queryWrapper.like(EvalTrainDatasetVersionPo::getName, nameLike));
        // 排除状态为删除的记录
        queryWrapper.ne(EvalTrainDatasetVersionPo::getStatus, TrainDatasetStatusEnum.Deleted.getCode());
        // 执行查询并返回结果
        return queryWrapper.list();
    }

    @Override
    public List<EvalTrainDatasetVersionPo> getValidByDatasetId(Long trainDatasetId) {
        return lambdaQuery()
                .eq(EvalTrainDatasetVersionPo::getDatasetId, trainDatasetId)
                .ne(EvalTrainDatasetVersionPo::getStatus, TrainDatasetStatusEnum.Deleted.getCode())
                .list();
    }

    @Override
    public void deleteByDatasetId(Long datasetId) {
        lambdaUpdate().eq(EvalTrainDatasetVersionPo::getDatasetId, datasetId)
                .set(EvalTrainDatasetVersionPo::getStatus, TrainDatasetStatusEnum.Deleted.getCode())
                .update();
    }

    @Override
    public void deleteById(Long trainDatasetVersionId) {
        lambdaUpdate().eq(EvalTrainDatasetVersionPo::getId, trainDatasetVersionId)
                .set(EvalTrainDatasetVersionPo::getStatus, TrainDatasetStatusEnum.Deleted.getCode())
                .update();
    }

    @Override
    public EvalTrainDatasetVersionPo getByDatasetIdAndVersionNumber(Long datasetId, Integer versionNumber) {
        return lambdaQuery()
                .eq(EvalTrainDatasetVersionPo::getDatasetId, datasetId)
                .eq(EvalTrainDatasetVersionPo::getVersion, versionNumber)
                .ne(EvalTrainDatasetVersionPo::getStatus, TrainDatasetStatusEnum.Deleted.getCode())
                .one();
    }

    @Override
    public List<EvalTrainDatasetVersionPo> getAllByDatasetId(Long datasetId) {
        return lambdaQuery()
                .eq(EvalTrainDatasetVersionPo::getDatasetId, datasetId)
                .list();
    }

    @Override
    public void updateStatusByTaskId(Long taskId, Integer status) {
        lambdaUpdate().eq(EvalTrainDatasetVersionPo::getTaskId, taskId)
                .set(EvalTrainDatasetVersionPo::getStatus, status)
                .update();
    }

    @Override
    public EvalTrainDatasetVersionPo getEvalTrainDatasetByTaskId(Long taskId) {
        return lambdaQuery().eq(EvalTrainDatasetVersionPo::getTaskId, taskId).one();
    }
}
