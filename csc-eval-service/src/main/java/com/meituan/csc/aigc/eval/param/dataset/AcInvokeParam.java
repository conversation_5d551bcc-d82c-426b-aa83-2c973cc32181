package com.meituan.csc.aigc.eval.param.dataset;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AcInvokeParam implements Serializable {
    private String interfaceId;
    /**
     * AC接口类型 1-Object类型 2-List类型
     */
    private Integer interfaceType;
    /**
     * 超时时间，单位毫秒
     */
    private Integer timeOut;
    private List<Param> inputParamList;
    private List<Param> outputParamList;

    @Data
    public static class Param implements Serializable {
        private String code;
        private String name;
        private String sourceCode;
        private Boolean isRequired;
        private Boolean isCheck;
        private String checkValue;
        private String checkFailMessage;
    }
}
