package com.meituan.csc.aigc.eval.param.pike;

import com.meituan.csc.aigc.eval.dto.pike.PikeEventDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PikeCacheParam implements Serializable {
    private String token;
    private PikeEventDTO.User user;

    public PikeCacheParam() {
    }

    public PikeCacheParam(String token, PikeEventDTO.User user) {
        this.token = token;
        this.user = user;
    }
}
