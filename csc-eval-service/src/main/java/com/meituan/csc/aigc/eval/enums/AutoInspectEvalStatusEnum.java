package com.meituan.csc.aigc.eval.enums;

public enum AutoInspectEvalStatusEnum {

    /**
     * query detail 状态
     */
    CREATING(0, "创建中"),
    PULL_DATA(1, "数据拉取中"),
    QUEUE(2, "排队中"),
    EVALUATING(3, "评测中"),
    FINISHED(4, "评测完成"),
    TIMEOUT(5, "超时"),
    FAILED(6, "失败");

    private int code;
    private String info;

    AutoInspectEvalStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    public int getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    public static AutoInspectEvalStatusEnum parse(int code) {
        for (AutoInspectEvalStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}
