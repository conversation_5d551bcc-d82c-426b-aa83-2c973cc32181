package com.meituan.csc.aigc.eval.enums.dataset;

import com.meituan.csc.aigc.eval.enums.SceneTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DatasetPullTemplateEnum {

    /**
     * 数据拉取模版枚举
     */
    JUDGE_OFFLINE_CASE(1, "judgeOfflineCase", "判责离线工单"),
    JUDGE_OFFLINE_TASK(2, "judgeOfflineTask", "判责离线Task"),
    AIDA_ONLINE(3, "judgeOnline", "AI搭在线"),
    AIDA_OFFLINE(4, "aidaOffline", "AI搭离线"),
    ;

    private final int code;
    private final String name;
    private final String description;

    public static String getTemplateName(Integer sceneId, Integer source) {
        if (SceneTypeEnum.PRIVACY_NUMBER.getCode() == sceneId) {
            if (DatasetUserSourceEnum.CASE.getCode() == source) {
                return JUDGE_OFFLINE_CASE.getName();
            } else if (DatasetUserSourceEnum.TASK.getCode() == source) {
                return JUDGE_OFFLINE_TASK.getName();
            }
        } else if (SceneTypeEnum.MANUAL_CHAT.getCode() == sceneId) {
            return AIDA_OFFLINE.getName();
        } else if (SceneTypeEnum.MODEL_LOG.getCode() == sceneId) {
            return AIDA_ONLINE.getName();
        }
        return null;
    }
}
