package com.meituan.csc.aigc.eval.dto.mq;

import lombok.Data;

/**
 * 机器人命中情况
 */
@Data
public class RobotInvokeLog {

    /**
     * 访问Id
     */
    private String visitId;

    /**
     * 会话Id
     */
    private Long sessionId;

    /**
     * 业务Id
     */
    private String businessTypeId;

    /**
     * 子业务Id
     */
    private String subBusinessTypeId;

    /**
     * 门户的userId
     */
    private Long userId;

    /**
     * 消息id
     */
    private Long messageId;

    /**
     * 用户查询
     */
    private String userQuery;

    /**
     * 标准问Id
     */
    private String typicalQuestionId;

    /**
     * 标准问名称
     */
    private String typicalQuestionName;

    /**
     * 触发时间
     */
    private String triggerTime;

    /**
     * 答案精确度
     */
    private String answerLevel;

    /**
     * 答案的Id faqId|TaskCode
     */
    private String replyId;

    /**
     * 答案的名称  faqTitle|TaskName
     */
    private String replyName;

    /**
     * 答案的类型
     */
    private String replyType;

    /**
     * 标准问触发类型
     */
    private Integer triggerType;

    /**
     * 标准问触发name
     */
    private String triggerName;

    /**
     * task执行实例ID
     */
    private String processInstanceId;

    /**
     * 知识库版本类型 formal / gray
     */
    private String knowledgeVersion;

    /**
     * 知识库版本号
     */
    private String knowledgeVersionNumber;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * FAQ ID
     */
    private Integer faqId;

    /**
     * FAQ 名称
     */
    private String faqName;

    /**
     * 任务Key
     */
    private String taskKey;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务版本
     */
    private String taskVersion;

    /**
     * 是否在Task中上报订单Id
     */
    private Boolean isInTask;

    /**
     * 任务触发来源
     */
    private String taskTriggerSource;

    /**
     * traceId
     */
    private String traceId;
}
