package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eval_task_log")
public class EvalTaskLogPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 日志类型 0-应用输出日志 1-评测日志
     */
    private Integer logType;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 数据集id
     */
    private Long datasetId;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * query唯一id
     */
    private Long queryId;

    /**
     * 评测结果唯一id
     */
    private Long queryDetailId;

    /**
     * 被测应用
     */
    private String testApplication;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 花费时间
     */
    private Integer costTime;

    /**
     * 花费token
     */
    private String costToken;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 执行是否成功，0-成功 1-失败
     */
    private Integer isSuccess;

    /**
     * 指标id
     */
    private Long metricId;


}
