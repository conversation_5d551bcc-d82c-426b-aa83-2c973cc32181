package com.meituan.csc.aigc.eval.enums.trainDataset;

public enum TrainDatasetStatusEnum {
    /**
     * 数据集详情状态
     */
    CREATING(0, "创建中"),
    CREATED(1, "创建完成"),
    RUNNING(2, "标注任务执行中"),
    FAIL(3, "标注任务执行失败"),
    FINISH(4, "标注任务执行成功"),
    Deleted(999,"已删除");

    private final int code;
    private final String description;

    TrainDatasetStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
