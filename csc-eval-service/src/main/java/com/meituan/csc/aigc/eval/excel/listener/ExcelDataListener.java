package com.meituan.csc.aigc.eval.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel数据监听器
 * <AUTHOR>
 * @date 2025-04-13
 */
public class ExcelDataListener extends AnalysisEventListener<Map<Integer, String>> {
    /**
     * 表头信息
     */
    private Map<Integer, String> headers;

    /**
     * 第一行表头信息
     */
    private Map<Integer, String> firstRowHeaders = new HashMap<>();

    /**
     * 第二行表头信息
     */
    private Map<Integer, String> secondRowHeaders = new HashMap<>();

    /**
     * 当前表头行数
     */
    private int currentHeadRowIndex = 0;

    /**
     * 数据列表
     */
    private List<Map<Integer, String>> dataList = new ArrayList<>();
    
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        int rowIndex = context.readRowHolder().getRowIndex();
        
        // 保存表头信息
        this.headers = headMap;
        
        // 区分不同行的表头
        if (rowIndex == 0) {
            firstRowHeaders.putAll(headMap);
            currentHeadRowIndex = 1;
        } else if (rowIndex == 1) {
            secondRowHeaders.putAll(headMap);
            currentHeadRowIndex = 2;
        }
    }
    
    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // 保存每行数据
        dataList.add(data);
    }
    
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 解析完成后的操作
    }
    
    public Map<Integer, String> getHeaders() {
        return headers;
    }
    
    public Map<Integer, String> getFirstRowHeaders() {
        return firstRowHeaders;
    }
    
    public Map<Integer, String> getSecondRowHeaders() {
        return secondRowHeaders;
    }
    
    public List<Map<Integer, String>> getDataList() {
        return dataList;
    }
}
