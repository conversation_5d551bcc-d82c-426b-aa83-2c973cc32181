package com.meituan.csc.aigc.eval.service.filter;

import com.sankuai.inf.octo.mns.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 责任链上下文,SpringBoot启动时执行
 * @param <T>
 */
@Component
@RequiredArgsConstructor
public final class AbstractFilterChainContext<T> implements CommandLineRunner {
    private final Map<String, List<AbstractFilterChainHandler>> abstractFilterChainHandlerContainer = new HashMap<>();

    @Autowired
    private ApplicationContext applicationContext;
    public void handler(String mark,T requestParam){
        List<AbstractFilterChainHandler> abstractFilterChainHandlerList = abstractFilterChainHandlerContainer.get(mark);
        if(CollectionUtil.isEmpty(abstractFilterChainHandlerList)){
            throw new RuntimeException(String.format("[%s] ChainFilter not found",mark));
        }
        abstractFilterChainHandlerList.forEach(abstractFilterChainHandler -> abstractFilterChainHandler.handler(requestParam));
    }

    @Override
    public void run(String... args) throws Exception {
        // 注册时获取所有责任链处理器
        Map<String,AbstractFilterChainHandler> chainFilterMap = applicationContext.getBeansOfType(AbstractFilterChainHandler.class);
        chainFilterMap.forEach((beanName,bean)->{
            List<AbstractFilterChainHandler> abstractFilterChainHandlerList = abstractFilterChainHandlerContainer.get(bean.mark());
            if (CollectionUtil.isEmpty(abstractFilterChainHandlerList)) {
                abstractFilterChainHandlerList = new ArrayList<>();
            }
            abstractFilterChainHandlerList.add(bean);
            abstractFilterChainHandlerContainer.put(bean.mark(), abstractFilterChainHandlerList);
        });
    }
}
