package com.meituan.csc.aigc.eval.dto.mark;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MarkTaskSessionDTO implements Serializable {
    /**
     * 会话唯一id
     */
    private String conversationId;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 标注状态
     */
    private Integer markStatus;
    /**
     * 当前用户标注状态
     */
    private Integer userMarkStatus;
    /**
     * 当前用户是否标注人
     */
    private Boolean isMarker;
    private String startTime;
    /**
     * 标注人列表
     */
    private List<String> markMisList;
}
