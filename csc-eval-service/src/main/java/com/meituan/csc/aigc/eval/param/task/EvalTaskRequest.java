package com.meituan.csc.aigc.eval.param.task;

import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import com.meituan.csc.aigc.eval.enums.CallTypeEnum;
import com.meituan.csc.aigc.eval.enums.task.TaskHistorySourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EvalTaskRequest extends EvalRequest implements Serializable {
    /**
     * query维度指标
     */
    private List<Integer> queryMetric;

    /**
     * session维度自动指标
     */
    private List<Integer> sessionMetric;

    /**
     * 评测阈值
     */
    private List<ScoreThresholdParam> scoreThreshold;

    /**
     * 模型输出获取方式
     */
    private Integer callType = CallTypeEnum.OFFLINE.getCode();

    /**
     * # 质检人列表，英文逗号分隔
     */
    private String inspectors;

    /**
     * 待测模型来源(待测应用来源) 0-评测系统 1-ai搭
     */
    private Integer applicationSource;

    /**
     * 应用配置列表
     */
    private List<String> applicationConfig;

    /**
     * 应用和模型映射关系
     */
    private Map<String, String> applicationModelMap;

    /**
     * 是否服体内部人员，用于判断是否使用评测的fridayKey
     */
    private Boolean isInner;

    /**
     * 输入来源 0-样本集 1-人工模拟
     *
     * @see com.meituan.csc.aigc.eval.enums.TaskInputSourceEnum
     */
    private Integer inputSource;

    /**
     * 模拟次数，人工模拟时必填
     */
    private Integer mockTimes;

    /**
     * 应用绑定字段
     */
    private Map<Long, List<TemplateFieldBindDTO>> bindFields;

    /**
     * 用户模拟器关联信息(robotMockBindFields)
     */
    private Map<Long, List<TemplateFieldBindDTO>> robotMockBindFields;
    /**
     * 用户模拟器id
     */
    private Long robotMockId;

    /**
     * 对话历史来源 1-预期结果 2-模型输出
     *
     * @see TaskHistorySourceEnum
     */
    private Integer historySource;

    /**
     * 应用话术字段映射关系
     */
    private Map<Long, String> applicationOutputMap;

    /**
     * # 任务类型 0-人工评测 1-自动评测 2-竞技评测 3-自动巡检 4-标注任务
     */
    private Integer taskType;

    /**
     * 评测开始时间
     */
    private Date startTime;

    private List<TaskMetricParam> metricList;

    /**
     * 大模型节点id
     */
    private String nodeId;

    private String nodeAppModelVersionId;

    /**
     * 节点id的调用密钥
     */
    private String nodeApiToken;
    /**
     * 是否评测任务
     */
    private Boolean whetherRegressDataset;

    /**
     * 训练集版本id
     */
    private Long versionId;

    /**
     * 标注任务的描述信息。
     */
    private String description;
}
