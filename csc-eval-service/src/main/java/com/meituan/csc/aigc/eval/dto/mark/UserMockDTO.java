package com.meituan.csc.aigc.eval.dto.mark;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserMockDTO implements Serializable {

    private List<Output> output;

    private List<ErrorMsg> errorMsg;

    private List<MarkSessionDetailDTO.Chat> chatList;

    @Data
    public static class Output implements Serializable {
        private Boolean isSuccess;
        private String application;
        private String result;
        private String messageId;
        private String message;
    }

    @Data
    public static class ErrorMsg implements Serializable {
        private String application;
        private String applicationName;
        private String message;
    }

    public UserMockDTO() {

    }

    public UserMockDTO(List<MarkSessionDetailDTO.Chat> chatList) {
        this.chatList = chatList;
    }
}
