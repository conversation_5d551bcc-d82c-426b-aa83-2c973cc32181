package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WorkbenchMetricsMappingDTO implements Serializable {
    /**
     * 机器指标id
     */
    private Long autoMetricId;
    /**
     * 人工指标id
     */
    private Long manualMetricId;

    public WorkbenchMetricsMappingDTO() {

    }

    /**
     * 构造函数
     *
     * @param autoMetricsId   机器指标id
     * @param manualMetricsId 人工指标id
     */
    public WorkbenchMetricsMappingDTO(Long autoMetricsId, Long manualMetricsId) {
        this.autoMetricId = autoMetricsId;
        this.manualMetricId = manualMetricsId;
    }
}
