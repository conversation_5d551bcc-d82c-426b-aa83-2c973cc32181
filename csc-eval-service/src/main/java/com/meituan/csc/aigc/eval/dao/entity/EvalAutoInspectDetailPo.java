package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 巡检详情表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eval_auto_inspect_detail")
public class EvalAutoInspectDetailPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 巡检详情Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 巡检任务执行频率类型
     */
    private Integer frequencyType;

    /**
     * 巡检任务执行频率对应的日子
     */
    private Integer frequencyDay;

    /**
     * 巡检任务对应的评测任务Id
     */
    private Long taskId;

    /**
     * 巡检任务对应的配置Id
     */
    private Long autoInspectConfigId;

    /**
     * 巡检任务对应的指标Id
     */
    private Long metricId;

    /**
     * 本次巡检的总会话数
     */
    private Long totalSessionCount;

    /**
     * 本次巡检的成功会话数
     */
    private Long successSessionCount;

    /**
     * 相比上次巡检的环比增长
     */
    private Double rate;

    /**
     * 创建时间
     */
    private Date gmtCreated;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 创建人
     */
    private String creatorMis;

    /**
     * 更新人
     */
    private String updaterMis;

    /**
     * 命中次数
     */
    private Long hitCount;

    /**
     * 状态
     */
    private Integer status;


}
