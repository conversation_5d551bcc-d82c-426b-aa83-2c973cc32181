package com.meituan.csc.aigc.eval.dto.workbench.process.detail;

import com.dianping.csc.eagle.query.dto.portal.RobotInvokeLog;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.meituan.csc.aigc.eval.dto.workbench.process.RobotSolution;
import com.meituan.csc.aigc.eval.dto.workbench.process.ServiceProcessDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户问题及解决方案详情
 *
 * <AUTHOR>
 */
@Data
public class RobotInvokeDetail<T extends RobotSolution> implements ServiceProcessDetail {

    @JsonProperty("messageId")
    private Long messageId;

    @JsonProperty("userInput")
    private String userInput;

    @JsonProperty("userInputType")
    private String userInputType;

    @JsonProperty("answerLevel")
    private String answerLevel;

    @JsonProperty("typicalQuestionId")
    private Integer typicalQuestionId;

    @JsonProperty("typicalQuestionName")
    private String typicalQuestionName;

    @JsonProperty("orderId")
    private String orderId;

    @JsonProperty("orderStatus")
    private String orderStatus;

    @JsonProperty("orderTag")
    private String orderTag;

    @JsonProperty("solutionType")
    private String solutionType;

    @JsonProperty("solution")
    private T solution;

    @JsonProperty("orderList")
    private List<RobotInvokeLog.OrderDTO> orderList;

    @JsonProperty("tagCodeList")
    private List<String> tagCodeList;

    @Data
    public static class OrderDTO implements Serializable {

        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 订单状态
         */
        private String orderStatus;

        /**
         * 触发时间
         */
        private Long triggerTime;
    }
}

