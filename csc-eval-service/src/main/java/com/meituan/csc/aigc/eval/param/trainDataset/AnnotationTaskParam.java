package com.meituan.csc.aigc.eval.param.trainDataset;

import com.meituan.csc.aigc.eval.dto.dataset.TemplateFieldBindDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 标注任务参数类，用于封装创建标注任务所需的参数信息。
 */
@Data
public class AnnotationTaskParam implements Serializable {
    /**
     * 训练数据集ID。
     */
    private Long trainDatasetId;

    /**
     * 数据集版本ID。
     */
    private Long versionId;

    /**
     * 标注方式（例如：1-人工标注，2-大模型标注）。
     */
    private Integer annotationType;

    /**
     * 标注任务的描述信息。
     */
    private String description;

    /**
     * 标注指标ID列表。
     */
    private List<Long> annotationIds;
    /**
     * 人工标注指标ID列表
     */
    private List<Long> manualMetricIds;

    private List<BindFieldParam> annotationList;
    /**
     * 人工标注 模板-关联列名
     */
    private List<TemplateFieldBindDTO> manualBindFields;

    private Long evalTaskId;

    @Data
    public static class BindFieldParam {
        private Long metricId;
        private List<TemplateFieldBindDTO> bindFields;
    }
}
