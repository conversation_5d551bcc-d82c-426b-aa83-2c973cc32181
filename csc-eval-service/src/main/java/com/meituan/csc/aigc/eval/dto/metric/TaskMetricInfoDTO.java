package com.meituan.csc.aigc.eval.dto.metric;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TaskMetricInfoDTO implements Serializable {
    private Integer metricId;

    private String outputKey;
    private String metricName;

    private Integer metricType;

    private Object range;

    private Map<String, String> inspectReference;
}
