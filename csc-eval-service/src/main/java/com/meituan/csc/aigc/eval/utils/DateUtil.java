package com.meituan.csc.aigc.eval.utils;

import com.dianping.cat.Cat;
import com.meituan.csc.aigc.eval.exception.EvalException;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class DateUtil {

    public static String getSimpleToday() {
        return getNow("yyyyMMdd");
    }

    public static String getNow() {
        return getNow("yyyy-MM-dd HH:mm:ss");
    }

    public static String getNow(String format) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.format(new Date());
    }

    /**
     * 将日期转换为当天的0点
     *
     * @param date 日期
     * @return 当天的0点
     */
    public static Date getDateZero(Date date) {
        if (date == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 在指定日期的基础上增加指定天数
     *
     * @param date 要增加天数的日期
     * @param days 要增加的天数
     * @return 增加天数后的日期
     */
    public static Date addDays(Date date, int days) {
        // 获取当前日期的 Calendar 实例
        Calendar calendar = Calendar.getInstance();
        // 设置初始日期为传入的 date
        calendar.setTime(date);
        // 在当前日期的基础上增加指定天数
        calendar.add(Calendar.DAY_OF_MONTH, days);
        // 返回增加天数后的日期
        return calendar.getTime();
    }


    public static Date parse(String date, String format) throws ParseException {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.parse(date);
    }

    public static Date parseCatchError(String date, String format) {
        try {
            return parse(date, format);
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
        }
        return null;
    }

    public static String format(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(date);
    }

    public static String addDays(String date, String format, int days) throws ParseException {
        if (StringUtils.isBlank(date)) {
            return date;
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        Date oldDate = parse(date, format);
        Date newDate = addDays(oldDate, days);
        return format(newDate, format);
    }

    public static String addDaysCatchError(String date, String format, int days) {
        try {
            return addDays(date, format, days);
        } catch (Exception e) {
            Cat.logError(new EvalException(e));
        }
        return date;
    }

    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        return calendar.getTime();
    }

    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }
}
