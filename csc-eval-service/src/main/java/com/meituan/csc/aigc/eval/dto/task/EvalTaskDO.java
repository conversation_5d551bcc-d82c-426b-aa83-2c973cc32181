package com.meituan.csc.aigc.eval.dto.task;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EvalTaskDO implements Serializable {

    private Long id;

    private Long taskId;

    private Long queryId;

    private String sessionId;

    private String input;

    private String output;

    private String params;

    private Integer batchNum;

    private Double evalScore;

    private String result;

    private Long aidaTaskDetailId;

    private Integer evalType;

    private Double scoreThreshold;
}
