package com.meituan.csc.aigc.eval.dao.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 质检工作台评价表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("inspect_workbench_eval")
public class InspectWorkbenchEvalPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 大模型会话ID
     */
    private String llmSessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 大模型消息ID
     */
    private String llmMessageId;

    /**
     * 大模型平台类型 1-AI搭 1-PB
     */
    private Integer platformType;

    /**
     * 赞踩状态 1-点赞 1-点踩
     */
    private Integer agreeStatus;

    /**
     * 质检时间
     */
    private Date evalTime;

    /**
     * 质检人
     */
    private String evalMis;


}
