package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.meituan.csc.aigc.eval.aop.workspace.WorkspaceContextHolder;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dao.entity.ApplicationConfigPo;
import com.meituan.csc.aigc.eval.dao.entity.EvalTaskPo;
import com.meituan.csc.aigc.eval.dao.entity.MetricConfigPo;
import com.meituan.csc.aigc.eval.dao.service.generator.ApplicationConfigGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalTaskGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.MetricConfigGeneratorService;
import com.meituan.csc.aigc.eval.dto.ModelResultDTO;
import com.meituan.csc.aigc.eval.dto.PageData;
import com.meituan.csc.aigc.eval.dto.application.ApplicationDTO;
import com.meituan.csc.aigc.eval.dto.application.ApplicationInfoDTO;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.param.PageParam;
import com.meituan.csc.aigc.eval.param.application.*;
import com.meituan.csc.aigc.eval.param.model.ModelConfigRequestParam;
import com.meituan.csc.aigc.eval.param.task.AidaModelConfig;
import com.meituan.csc.aigc.eval.param.task.TaskConditionParam;
import com.meituan.csc.aigc.eval.param.task.TaskMetricParam;
import com.meituan.csc.aigc.eval.proxy.AidaInvokeServiceProxy;
import com.meituan.csc.aigc.eval.proxy.GptRequestServiceProxy;
import com.meituan.csc.aigc.eval.proxy.PbServiceProxy;
import com.meituan.csc.aigc.eval.service.ApplicationExecuteService;
import com.meituan.csc.aigc.eval.service.PermissionService;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.CommonEvalStrategyService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.csc.aigc.eval.utils.DateUtil;
import com.meituan.csc.aigc.eval.utils.FileNameUtil;
import com.meituan.csc.aigc.runtime.dto.agent.SlotInfoDTO;
import com.meituan.csc.aigc.runtime.inner.dto.InnerAppConfigDTO;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ApplicationExecuteServiceImpl implements ApplicationExecuteService {

    @Autowired
    private AidaInvokeServiceProxy aidaInvokeServiceProxy;

    @Autowired
    private PbServiceProxy pbServiceProxy;

    @Autowired
    private GptRequestServiceProxy gptRequestServiceProxy;

    @Autowired
    private ApplicationConfigGeneratorService applicationConfigGeneratorService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private CommonEvalStrategyService commonEvalStrategyService;

    @Autowired
    private ApplicationExecuteService applicationExecuteService;

    @Autowired
    private EvalTaskGeneratorService evalTaskGeneratorService;
    @Autowired
    private MetricConfigGeneratorService metricConfigGeneratorService;

    @Autowired
    private ExcelService excelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ApplicationParam applicationParam) {
        CommonUtils.checkEval(applicationParam != null && StringUtils.isNotBlank(applicationParam.getName()), "应用名称不能为空");
        String workspaceId = WorkspaceContextHolder.getWorkspaceId();
        // 根据workspace校验应用名称
        ApplicationConditionParam condition = new ApplicationConditionParam();
        condition.setWorkspaceId(workspaceId);
        condition.setName(applicationParam.getName());
        List<ApplicationConfigPo> applicationList = applicationConfigGeneratorService.getByCondition(condition);
        CommonUtils.checkEval(CollectionUtils.isEmpty(applicationList), "应用名称在空间中已存在");
        // 保存应用
        ApplicationConfigPo applicationConfigPo = new ApplicationConfigPo();
        applicationConfigPo.setPlatformWorkspace(workspaceId);
        applicationConfigPo.setSource(applicationParam.getSource());
        applicationConfigPo.setType(applicationParam.getType() == null ? ApplicationTypeEnum.EVAL.getCode() : applicationParam.getType());
        applicationConfigPo.setName(applicationParam.getName());
        if (applicationConfigPo.getSource() == null || ApplicationSourceEnum.AIDA_SYSTEM.getCode() == applicationConfigPo.getSource()) {
            saveAidaConfig(applicationParam, applicationConfigPo);
        } else {
            applicationConfigPo.setRobotId(String.valueOf(applicationParam.getPbSceneId()));
        }
        applicationConfigPo.setDescription(applicationParam.getDescription());
        applicationConfigPo.setCreatorMis(UserUtils.getUser().getLogin());
        Date date = new Date();
        applicationConfigPo.setGmtCreated(date);
        applicationConfigPo.setGmtModified(date);
        applicationConfigPo.setExtra(buildExtra(applicationParam));
        applicationConfigGeneratorService.save(applicationConfigPo);
        return applicationConfigPo.getId();
    }

    @Override
    public ApplicationConfigPo getOrCreateFromThirdSystem(ApplicationParam applicationParam) {
        ApplicationConditionParam condition = new ApplicationConditionParam();
        if (applicationParam.getPbSceneId() != null) {
            condition.setRobotId(String.valueOf(applicationParam.getPbSceneId()));
            condition.setWorkspaceId(applicationParam.getAidaModelConfig().getWorkspaceId()); // 错题集存放工作空间ID
        } else {
            condition.setWorkspaceId(applicationParam.getAidaModelConfig().getWorkspaceId());
            condition.setAppId(applicationParam.getAidaModelConfig().getApplicationId());
            condition.setRobotId(applicationParam.getAidaModelConfig().getModelConfigVersionId());
        }
        condition.setSource(applicationParam.getSource());
        List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByCondition(condition, true);
        if (CollectionUtils.isNotEmpty(applicationConfigList)) {
            ApplicationConfigPo applicationConfigPo = applicationConfigList.get(0);
            if (StringUtils.isNotBlank(applicationParam.getNewName()) && !applicationParam.getNewName().equals(applicationConfigPo.getName())) {
                applicationConfigPo.setName(applicationParam.getNewName());
                applicationConfigGeneratorService.updateById(applicationConfigPo);
            }
            return applicationConfigPo;
        }
        ApplicationConfigPo applicationConfigPo = new ApplicationConfigPo();
        if (applicationParam.getPbSceneId() != null) {
            applicationConfigPo.setRobotId(String.valueOf(applicationParam.getPbSceneId()));
            applicationConfigPo.setPlatformWorkspace(applicationParam.getAidaModelConfig().getWorkspaceId()); // 错题集存放工作空间ID
        } else {
            saveAidaConfig(applicationParam, applicationConfigPo);
        }
        applicationConfigPo.setSource(applicationParam.getSource());
        applicationConfigPo.setType(applicationParam.getType() == null ? ApplicationTypeEnum.EVAL.getCode() : applicationParam.getType());
        applicationConfigPo.setName(StringUtils.isNotBlank(applicationParam.getNewName()) ? applicationParam.getNewName() : applicationParam.getName());
        applicationConfigPo.setCreatorMis(applicationParam.getCreateMis());
        Date date = new Date();
        applicationConfigPo.setGmtCreated(date);
        applicationConfigPo.setGmtModified(date);
        applicationConfigGeneratorService.save(applicationConfigPo);
        return applicationConfigPo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ApplicationParam applicationParam) {
        ApplicationConfigPo applicationConfigPo = applicationConfigGeneratorService.getById(applicationParam.getId());
        CommonUtils.checkEval(applicationConfigPo != null, "应用不存在");
        permissionService.checkPermission(applicationConfigPo.getCreatorMis(), "当前登录用户非创建人，无更新权限");
        if (StringUtils.isNotBlank(applicationParam.getName())) {
            ApplicationConditionParam condition = new ApplicationConditionParam();
            condition.setCreateMis(UserUtils.getUser().getLogin());
            condition.setName(applicationParam.getName());
            List<ApplicationConfigPo> applicationList = applicationConfigGeneratorService.getByCondition(condition);
            CommonUtils.checkEval(CollectionUtils.isEmpty(applicationList) || (applicationList.size() == 1 && applicationList.get(0).getId().equals(applicationConfigPo.getId())), "应用名称已存在");
        }
        if (applicationParam.getSource() != null) {
            applicationConfigPo.setSource(applicationParam.getSource());
        }
        if (applicationParam.getSource() == null || ApplicationSourceEnum.AIDA_SYSTEM.getCode() == applicationParam.getSource()) {
            if (applicationParam.getAidaModelConfig() != null && !isSameAidaConfig(applicationParam.getAidaModelConfig(), applicationConfigPo)) {
                saveAidaConfig(applicationParam, applicationConfigPo);
            }
        } else {
            if (applicationParam.getPbSceneId() != null) {
                applicationConfigPo.setRobotId(String.valueOf(applicationParam.getPbSceneId()));
            }
        }
        if (StringUtils.isNotBlank(applicationParam.getName())) {
            applicationConfigPo.setName(applicationParam.getName());
        }
        if (StringUtils.isNotBlank(applicationParam.getDescription())) {
            applicationConfigPo.setDescription(applicationParam.getDescription());
        }
        if (StringUtils.isNotBlank(applicationParam.getScriptModelName())) {
            applicationConfigPo.setExtra(buildExtra(applicationParam));
        }
        applicationConfigPo.setGmtModified(new Date());
        applicationConfigGeneratorService.updateById(applicationConfigPo);
    }

    private boolean isSameAidaConfig(AidaModelConfig aidaModelConfig, ApplicationConfigPo applicationConfigPo) {
        return aidaModelConfig.getApplicationId().equals(applicationConfigPo.getPlatformApp()) && aidaModelConfig.getModelConfigVersionId().equals(applicationConfigPo.getRobotId()) && aidaModelConfig.getWorkspaceId().equals(applicationConfigPo.getPlatformWorkspace());
    }

    @Override
    public PageData<ApplicationDTO> page(PageParam<ApplicationConditionParam> pageParam) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByCondition(pageParam.getCondition(), true);
        ZebraForceMasterHelper.clearLocalContext();
        if (CollectionUtils.isEmpty(applicationConfigList)) {
            return PageData.emptyData(pageParam.getPageNum(), pageParam.getPageSize());
        }
        applicationConfigList = applicationConfigList.stream().filter(applicationConfigPo -> applicationConfigPo.getType() == null || applicationConfigPo.getType() == ApplicationTypeEnum.EVAL.getCode()).collect(Collectors.toList());
        int totalSize = applicationConfigList.size();
        List<ApplicationConfigPo> pageDataList = PageData.page(pageParam.getPageNum(), pageParam.getPageSize(), applicationConfigList);
        return convertApplicationPageInfo(totalSize, pageParam.getPageNum(), pageParam.getPageSize(), pageDataList);
    }

    @Override
    public Long copy(Long applicationId, String newApplicationName) {
        ApplicationConfigPo oldApplicationConfigPo = applicationConfigGeneratorService.getById(applicationId);
        CommonUtils.checkEval(oldApplicationConfigPo != null, "应用不存在");
        ApplicationConfigPo applicationConfigPo = new ApplicationConfigPo();
        applicationConfigPo.setName(newApplicationName);
        applicationConfigPo.setModelConfig(oldApplicationConfigPo.getModelConfig());
        applicationConfigPo.setPrompt(oldApplicationConfigPo.getPrompt());
        applicationConfigPo.setPlatformApp(oldApplicationConfigPo.getPlatformApp());
        applicationConfigPo.setPlatformWorkspace(oldApplicationConfigPo.getPlatformWorkspace());
        applicationConfigPo.setDescription(oldApplicationConfigPo.getDescription());
        applicationConfigPo.setRobotId(oldApplicationConfigPo.getRobotId());
        applicationConfigPo.setCreatorMis(UserUtils.getUser().getLogin());
        applicationConfigPo.setType(ApplicationTypeEnum.EVAL.getCode());
        applicationConfigPo.setSource(oldApplicationConfigPo.getSource());
        Date date = new Date();
        applicationConfigPo.setGmtCreated(date);
        applicationConfigPo.setGmtModified(date);
        applicationConfigGeneratorService.save(applicationConfigPo);
        return applicationConfigPo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long applicationId) {
        ApplicationConfigPo applicationConfigPo = applicationConfigGeneratorService.getById(applicationId);
        CommonUtils.checkEval(applicationConfigPo != null, "应用不存在");
        permissionService.checkPermission(applicationConfigPo.getCreatorMis(), "当前登录用户无删除权限");
        CommonUtils.checkEval(applicationConfigPo.getType() != null && applicationConfigPo.getType() == ApplicationTypeEnum.EVAL.getCode(), "指标关联的应用不能删除");
        TaskConditionParam condition = new TaskConditionParam();
        condition.setApplicationId(applicationId);
        List<EvalTaskPo> taskList = evalTaskGeneratorService.getByCondition(condition);
        CommonUtils.checkEval(CollectionUtils.isEmpty(taskList), "应用已关联任务，不允许删除");
        applicationConfigGeneratorService.removeById(applicationId);
    }

    @Override
    public ApplicationParam getById(Long applicationId) {
        ApplicationConfigPo applicationConfigPo = applicationConfigGeneratorService.getById(applicationId);
        CommonUtils.checkEval(applicationConfigPo != null, "应用不存在");
        ApplicationParam applicationParam = new ApplicationParam();
        applicationParam.setId(applicationConfigPo.getId());
        applicationParam.setName(applicationConfigPo.getName());
        applicationParam.setDescription(applicationConfigPo.getDescription());
        AidaModelConfig aidaModelConfig = new AidaModelConfig();
        aidaModelConfig.setWorkspaceId(applicationConfigPo.getPlatformWorkspace());
        aidaModelConfig.setApplicationId(applicationConfigPo.getPlatformApp());
        aidaModelConfig.setModelConfigVersionId(applicationConfigPo.getRobotId());
        applicationParam.setAidaModelConfig(aidaModelConfig);
        applicationParam.setSource(applicationConfigPo.getSource() == null ? ApplicationSourceEnum.AIDA_SYSTEM.getCode() : applicationConfigPo.getSource());
        if (ApplicationSourceEnum.PB_SYSTEM.getCode() == applicationParam.getSource()) {
            applicationParam.setPbSceneId(Long.valueOf(applicationConfigPo.getRobotId()));
        }
        if (StringUtils.isNotBlank(applicationConfigPo.getExtra())) {
            ApplicationExtraParam extraParam = JSON.parseObject(applicationConfigPo.getExtra(), ApplicationExtraParam.class);
            applicationParam.setScriptModelName(extraParam.getScriptModelName());
            applicationParam.setApplicationSource(extraParam.getApplicationSource());
        }
        return applicationParam;
    }

    @Override
    public List<ApplicationDTO> list() {
        ApplicationConditionParam condition = new ApplicationConditionParam();
        condition.setWorkspaceId(WorkspaceContextHolder.getWorkspaceId());
        List<ApplicationConfigPo> applicationConfigPos = applicationConfigGeneratorService.getByCondition(condition);
        return applicationConfigPos.stream()
                .filter(applicationConfigPo -> applicationConfigPo.getType() == null || applicationConfigPo.getType() == ApplicationTypeEnum.EVAL.getCode()).map(
                        applicationConfigPo -> {
                            ApplicationDTO applicationDTO = new ApplicationDTO();
                            applicationDTO.setId(applicationConfigPo.getId());
                            applicationDTO.setName(applicationConfigPo.getName());
                            return applicationDTO;
                        }
                ).collect(Collectors.toList());
    }

    @Override
    public ApplicationInfoDTO getDatasetApplicationInfo(DatasetApplicationParam param) {
        ApplicationInfoDTO datasetApplicationInfo = new ApplicationInfoDTO();
        ApplicationInfoDTO applicationInfo = getApplicationInfo(param);
        datasetApplicationInfo.setApplicationType(applicationInfo.getApplicationType());
        if (param.getUploadType() != null) {
            List<ApplicationInfoDTO.Param> applicationParamList = Optional.ofNullable(applicationInfo.getApplicationParamList()).orElse(Collections.emptyList()).stream().map(applicationParam -> new ApplicationInfoDTO.Param(applicationParam.getFieldName(), TemplateFieldEnum.PARAMS.getCode(), applicationParam.getIsRequire(), applicationParam.getFieldCode())).collect(Collectors.toList());
            datasetApplicationInfo.setApplicationParamList(applicationParamList);
            List<ApplicationInfoDTO.Param> systemParamList = buildSystemParamList(applicationInfo, param);
            datasetApplicationInfo.setSystemParamList(systemParamList);
        }
        return datasetApplicationInfo;
    }


    /**
     * 获取分类标注时需要的机器人变量-分类版
     * @param param
     * @return
     */
    @Override
    public ApplicationInfoDTO getDatasetApplicationInfoClassify(DatasetApplicationParam param) {
        ApplicationInfoDTO datasetApplicationInfo = new ApplicationInfoDTO();
        ApplicationInfoDTO applicationInfo = getApplicationInfo(param);
        List<ApplicationInfoDTO.Param> paramList = new ArrayList<>();
        if (param.getUploadType() != null) {
            //应用参数列表
            List<ApplicationInfoDTO.Param> applicationParamList = applicationInfo.getApplicationParamList().stream().map(applicationParam -> new ApplicationInfoDTO.Param(applicationParam.getFieldName(), TemplateFieldEnum.PARAMS.getCode(), applicationParam.getIsRequire(), applicationParam.getFieldCode(), ParamTypeEnum.APPLICATION.getCode())).collect(Collectors.toList());
            Optional.of(applicationParamList).ifPresent(paramList::addAll);
            //系统变量列表
            Optional.of(buildSystemParamList(applicationInfo, param)).ifPresent(paramList::addAll);
        }
        //判断用户输入来源
        buildInputSourceParamList(param, paramList, applicationInfo);
        //判断指标   自动指标 变量-
        buildMetricIdBindParamList(param, paramList, applicationInfo);
        datasetApplicationInfo.setParamList(paramList);
        return datasetApplicationInfo;
    }

    private void buildMetricIdBindParamList(DatasetApplicationParam param, List<ApplicationInfoDTO.Param> paramList, ApplicationInfoDTO applicationInfo) {
        List<Long> metricList = Optional.ofNullable(param.getMetricList()).orElse(Collections.emptyList())
                .stream()
                .map(TaskMetricParam::getMetricId)
                .distinct()
                .collect(Collectors.toList());
        //todo
        //List<Long> autoMetricList = Optional.ofNullable(Lion.getList(ConfigUtil.getAppkey(), LionConstants.EVAL_TASK_METRIC_SHOW_PARAM, Integer.class, new ArrayList<>())).orElse(Collections.emptyList()).stream().map(Long::valueOf).collect(Collectors.toList());
        //metricList.retainAll(autoMetricList);
        //三个内置指标(包含、相似、全等)增加展示项目：预期结果(必填)
        List<Long> metricIds = metricList.stream().map(Long::intValue).filter(EvalMetricTypeEnum.COMMON_METRIC::contains).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(metricIds)) {
            paramList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.EXPECT, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
        }
        //包含 全等、相似 除外
        //查询到全部指标
        List<Long> metricIdIts = metricList.stream().map(Long::intValue).filter(metricId -> Objects.nonNull(metricId) && !EvalMetricTypeEnum.COMMON_METRIC.contains(metricId)).map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(metricIdIts)) {
            return;
        }
        //若有指标变量，删除前面加的应用变量
        paramList.removeIf(dto -> ParamTypeEnum.APPLICATION.getCode().equals(dto.getType()));
        getMetricVariable(param, paramList, metricIdIts);
    }

    public void getMetricVariable(DatasetApplicationParam param, List<ApplicationInfoDTO.Param> paramList, List<Long> metricIdIts) {
        List<MetricConfigPo> metricConfigPos = metricConfigGeneratorService.getByIdList(metricIdIts);
        //从指标取到关联的应用
        List<Long> applicationIds = Optional.ofNullable(metricConfigPos)
                .orElse(Collections.emptyList())
                .stream()
                .map(MetricConfigPo::getApplicationId)
                .distinct()
                .collect(Collectors.toList());
        //取应用下面的变量
        param.setApplicationIdList(applicationIds);
        //Collection<ApplicationConfigPo> applicationConfigPos = applicationConfigGeneratorService.listByIds(applicationIds);

        ApplicationInfoDTO dto = getApplicationInfo(param);
        Optional.of(dto.getApplicationParamList()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(applicationParam -> {
                            return new ApplicationInfoDTO.Param(applicationParam.getFieldName(),
                                    TemplateFieldEnum.PARAMS.getCode(),
                                    applicationParam.getIsRequire(),
                                    applicationParam.getFieldCode(),
                                    ParamTypeEnum.METRIC.getCode());
                        }).collect(Collectors.toList()))
                .ifPresent(paramList::addAll);
    }

    private void buildInputSourceParamList(DatasetApplicationParam param, List<ApplicationInfoDTO.Param> paramList, ApplicationInfoDTO applicationInfo) {
        if (Objects.isNull(param.getInputSource())) {
            return;
        }
        if (param.getInputSource().equals(TaskInputSourceEnum.MANUAL_MOCK.getCode())) {
            paramList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.SUMMARY, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
        } else if (param.getInputSource().equals(TaskInputSourceEnum.ROBOT_MOCK.getCode())) {
            Optional.ofNullable(applicationInfo.getApplicationParamList().stream().filter(applicationParam -> new ApplicationInfoDTO.Param(applicationParam.getFieldName(), TemplateFieldEnum.PARAMS.getCode(), applicationParam.getIsRequire(), applicationParam.getFieldCode(), ParamTypeEnum.SIMULATOR.getCode()).getIsRequire()).collect(Collectors.toList())).ifPresent(paramList::addAll);
        }
    }

    @Override
    public ApplicationInfoDTO getApplicationInfo(ApplicationListInfoParam applicationParam) {
        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        if (applicationParam.getApplicationSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(applicationParam.getApplicationIdList());
            Map<Long, ApplicationConfigPo> applicationConfigMap = applicationConfigList.stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity()));
            applicationParam.getApplicationIdList().forEach(applicationId -> {
                ApplicationConfigPo applicationConfigPo = applicationConfigMap.get(applicationId);
                ApplicationInfoDTO applicationInfo = createApplicationInfo(applicationConfigPo);
                checkAndMergeApplication(applicationInfo, applicationInfoDTO);
            });
        } else {
            applicationParam.getAidaModelConfigList().forEach(aidaModelConfig -> {
                ApplicationInfoDTO applicationInfo = getAidaApplicationInfo(aidaModelConfig.getApplicationId(), aidaModelConfig.getModelConfigVersionId());
                checkAndMergeApplication(applicationInfo, applicationInfoDTO);
            });
        }
        return applicationInfoDTO;
    }

    @Override
    public String downloadDatasetTemplate(DatasetTemplateRequest param) {
        // todo 接受应用列表的接口 都需要检查应用数量
        List<ExcelService.ColorfulHead> templateParam = getDatasetTemplateParam(param);
        log.info("数据集模板下载, 模板参数:{}", JSON.toJSONString(templateParam));

        // 生成文件名
        String filename = generateDatasetFileName(param);
        // 生成S3文件名，其支持1-1024字节，添加毫秒级时间戳不会超长
        String s3Filename = String.format("%s-%d.xlsx", filename, System.currentTimeMillis());
        // 生成模板并上传
        return excelService.generateSingletonColorfulHeadExcelAndUpload(templateParam, Collections.emptyList(), "数据集模板", s3Filename, filename, "xlsx", UserUtils.getUser().getLogin());
    }

    private List<ExcelService.ColorfulHead> getDatasetTemplateParam(DatasetTemplateRequest param) {
        // 获取应用信息
        ApplicationInfoDTO applicationInfo = getApplicationInfo(param);
        Map<String, InnerAppConfigDTO> map = new HashMap<>();
        log.info("数据集模板获取应用信息, applicationInfo:{}", JSON.toJSONString(applicationInfo));

        // 提取系统变量, 使用Name
        List<ExcelService.ColorfulHead> templateParam = buildSystemParamList(applicationInfo, param).stream()
                .map(p -> new ExcelService.ColorfulHead(p.getFieldName(), p.getIsRequire() ? IndexedColors.RED : IndexedColors.BLACK))
                .collect(Collectors.toList());
        // 提取应用变量, 使用Code
        if (CollectionUtils.isNotEmpty(applicationInfo.getApplicationParamList())) {
            templateParam.addAll(applicationInfo.getApplicationParamList().stream()
                    .map(p -> new ExcelService.ColorfulHead(p.getFieldName(), p.getIsRequire() ? IndexedColors.RED : IndexedColors.BLACK))
                    .collect(Collectors.toList()));
        }
        // 应用来源为评测系统
        if (param.getApplicationSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            return templateParam;
        }

        param.getAidaModelConfigList().forEach(config -> {
            InnerAppConfigDTO aidaRobotInfo = aidaInvokeServiceProxy.getAidaRobotInfo(config.getApplicationId(), config.getModelConfigVersionId());
            if (aidaRobotInfo != null) {
                map.put(config.getModelConfigVersionId(), aidaRobotInfo);
            } else {
                log.info("获取到的 aidaRobotInfo 为空, applicationId: {}, modelConfigVersionId: {}", config.getApplicationId(), config.getModelConfigVersionId());
            }
        });
        log.info("数据集模板获取ai搭机器人信息, aidaRobotInfo:{}", JSON.toJSONString(map));

        // 提取结构化出参
//        Set<String> outputKeySet = new HashSet<>();
//        for (AidaModelConfig config : param.getAidaModelConfigList()) {
//            InnerAppConfigDTO innerAppConfigDTO = map.get(config.getModelConfigVersionId());
//            if (innerAppConfigDTO.getOutputForm() != null && BooleanUtils.isTrue(innerAppConfigDTO.getOutputForm().getEnabled())) {
//                innerAppConfigDTO.getOutputForm().getParams().stream()
//                        .filter(outputParam -> outputKeySet.add(outputParam.getKey()))
//                        .forEach(outputParam -> {
//                            // 结构化出参默认设置为必填项，使用Key
//                            templateParam.add(new ExcelService.ColorfulHead(outputParam.getName(), IndexedColors.RED));
//                        });
//            }
//        }
        return templateParam;
    }


    /**
     * 获取需要配置的变量信息
     * 在线+单轮：变量、预期输出（必填）
     * 在线+多轮：会话id（必填）、变量、输入内容（必填）、预期输出（必填）、对话摘要（模拟场景下必填）
     * 离线+单轮：变量、预期输出（必填）、输出内容+应用名称（必填）
     * 离线+多轮：会话id（必填）、变量、输入内容（必填）、预期输出（必填）、对话摘要（模拟场景下必填）、输出内容+应用名称（必填）
     *
     * @param applicationInfo 应用信息
     * @param param           请求餐素
     * @return 需要配置的变量信息
     */
    private List<ApplicationInfoDTO.Param> buildSystemParamList(ApplicationInfoDTO applicationInfo, DatasetApplicationParam param) {
        List<ApplicationInfoDTO.Param> systemParamList = new ArrayList<>();
        if (Objects.nonNull(applicationInfo.getApplicationType())) {
            if (applicationInfo.getApplicationType() == AbilityEnum.SINGLE_ROUND.getCode()) {
                systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.EXPECT, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
            } else if (applicationInfo.getApplicationType() == AbilityEnum.MULTI_ROUND.getCode()) {
                systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.SESSION_ID, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
                systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.INPUT, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
                systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.EXPECT, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
                // 模拟场景下对话摘要必填
                if (param.getInputSource() != null && param.getInputSource() != TaskInputSourceEnum.DATASET.getCode()) {
                    systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.SUMMARY, Boolean.TRUE, ParamTypeEnum.SYSTEM.getCode()));
                }
            }
        }
        if (param.getUploadType() == UploadTypeEnum.CUSTOM_TEMPLATE_UPLOAD.getCode()) {
            systemParamList.addAll(buildOutputContent(param));
        }
        //添加业务参数
        addBusinessParam(param, systemParamList);

        return systemParamList;
    }

    /**
     * 添加业务参数
     * @param param
     * @param systemParamList
     */
    private static void addBusinessParam(DatasetApplicationParam param, List<ApplicationInfoDTO.Param> systemParamList) {
        String spaces = Lion.getString(ConfigUtil.getAppkey(), LionConstants.EVAL_TASK_BUSINESS_PARAM_PERMISSION);
        if(StringUtils.isNotBlank(spaces)){
            Map<String, List<String>> spaceMap = JSON.parseObject(spaces, new TypeReference<Map<String, List<String>>>() {
            });
            if(MapUtils.isNotEmpty(spaceMap) && CollectionUtils.isNotEmpty(param.getAidaModelConfigList())){
                for (AidaModelConfig aidaModelConfig : param.getAidaModelConfigList()) {
                    //只要有满足的，则全部添加，只添加一次即可
                    if (spaceMap.containsKey(aidaModelConfig.getWorkspaceId()) && spaceMap.get(aidaModelConfig.getWorkspaceId()).contains(aidaModelConfig.getApplicationId())) {
                        //  添加系统参数列表，类型为业务参数包括额外信息、附件、历史记录和自定义知识等参数。
                        systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.EXTRAINFO, Boolean.FALSE, ParamTypeEnum.BUSINESS.getCode()));
                        systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.ATTACHMENTS, Boolean.FALSE, ParamTypeEnum.BUSINESS.getCode()));
                        systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.HISTORIES, Boolean.FALSE, ParamTypeEnum.BUSINESS.getCode()));
                        systemParamList.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.CUSTOM_KNOWLEDGE, Boolean.FALSE, ParamTypeEnum.BUSINESS.getCode()));
                        break;
                    }
                }
            }

        }
    }

    private String buildExtra(ApplicationParam applicationParam) {
        if (StringUtils.isBlank(applicationParam.getScriptModelName())) {
            return null;
        }
        ApplicationExtraParam extraParam = new ApplicationExtraParam();
        extraParam.setScriptModelName(applicationParam.getScriptModelName());
        extraParam.setApplicationSource(applicationParam.getApplicationSource());
        return JSON.toJSONString(extraParam);
    }

    private void saveAidaConfig(ApplicationParam applicationParam, ApplicationConfigPo applicationConfigPo) {
        applicationConfigPo.setPlatformWorkspace(applicationParam.getAidaModelConfig().getWorkspaceId());
        applicationConfigPo.setPlatformApp(applicationParam.getAidaModelConfig().getApplicationId());
        applicationConfigPo.setRobotId(applicationParam.getAidaModelConfig().getModelConfigVersionId());
        InnerAppConfigDTO app = aidaInvokeServiceProxy.getAidaRobotInfo(applicationParam.getAidaModelConfig().getApplicationId(), applicationParam.getAidaModelConfig().getModelConfigVersionId());
        CommonUtils.checkEval(app != null, "获取ai搭配置失败，请稍后重试或联系ai搭值班");
        applicationConfigPo.setModelConfig(buildModelConfig(app));
        applicationConfigPo.setPrompt(app.getPrompt());
    }

    private String buildModelConfig(InnerAppConfigDTO app) {
        ModelConfigRequestParam param = new ModelConfigRequestParam();
        param.setModelName(app.getModelName());
        if (app.getModelParamConfig() != null) {
            Map<String, Object> modelParam = commonEvalStrategyService.buildModelParam(app.getModelParamConfig());
            param.setModelParam(modelParam);
        }
        return JSON.toJSONString(param);
    }

    private List<ApplicationInfoDTO.Param> buildOutputContent(DatasetApplicationParam param) {
        List<ApplicationInfoDTO.Param> outputContent = new ArrayList<>();
        if (param.getApplicationSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            List<ApplicationConfigPo> applicationConfigList = applicationConfigGeneratorService.getByIdList(param.getApplicationIdList());
            Map<Long, ApplicationConfigPo> applicationConfigMap = applicationConfigList.stream().collect(Collectors.toMap(ApplicationConfigPo::getId, Function.identity()));
            for (Long applicationId : param.getApplicationIdList()) {
                outputContent.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.REPLY.getInfo() + "-" + applicationConfigMap.get(applicationId).getName(), TemplateFieldEnum.REPLY.getCode(), Boolean.TRUE, String.valueOf(applicationId), ParamTypeEnum.SYSTEM.getCode()));
            }
        } else {
            for (AidaModelConfig aidaModelConfig : param.getAidaModelConfigList()) {
                ApplicationParam applicationParam = new ApplicationParam();
                applicationParam.setName("ai搭机器人:" + aidaModelConfig.getModelConfigVersionId());
                applicationParam.setSource(ApplicationSourceEnum.VIRTUAL.getCode());
                param.setCreateMis(param.getCreateMis());
                applicationParam.setAidaModelConfig(aidaModelConfig);
                ApplicationConfigPo applicationConfigPo = applicationExecuteService.getOrCreateFromThirdSystem(applicationParam);
                outputContent.add(new ApplicationInfoDTO.Param(TemplateFieldEnum.REPLY.getInfo() + "-" + aidaModelConfig.getModelConfigVersionName(), TemplateFieldEnum.REPLY.getCode(), Boolean.TRUE, String.valueOf(applicationConfigPo.getId()), ParamTypeEnum.SYSTEM.getCode()));
            }
        }
        return outputContent;
    }

    private void checkAndMergeApplication(ApplicationInfoDTO applicationInfo, ApplicationInfoDTO result) {
        CommonUtils.checkEval(applicationInfo.getApplicationType() != null, "获取应用类型信息错误，请重试");
        CommonUtils.checkEval(applicationInfo.getApplicationType() != ApplicationAbilityEnum.RULE.getCode(), "暂不支持规则应用类型");
        CommonUtils.checkEval(result.getApplicationType() == null || applicationInfo.getApplicationType().equals(result.getApplicationType()), "评测的应用类型必须一致，请检查应用配置");
        if (result.getApplicationParamList() == null) {
            result.setApplicationParamList(new ArrayList<>());
        }
        result.setApplicationType(applicationInfo.getApplicationType());
        if (CollectionUtils.isNotEmpty(applicationInfo.getApplicationParamList())) {
            applicationInfo.getApplicationParamList().forEach(param -> {
                if (result.getApplicationParamList().stream().noneMatch(p -> p.getFieldCode().equals(param.getFieldCode()))) {
                    result.getApplicationParamList().add(param);
                }
            });
        }
    }

    @Override
    public ApplicationInfoDTO createApplicationInfo(ApplicationConfigPo applicationConfigPo) {
        ApplicationInfoDTO applicationInfo;
        if (applicationConfigPo.getSource() != null && applicationConfigPo.getSource() == ApplicationSourceEnum.PB_SYSTEM.getCode()) {
            applicationInfo = getPbApplicationInfo(applicationConfigPo.getRobotId());
        } else {
            applicationInfo = getAidaApplicationInfo(applicationConfigPo.getPlatformApp(), applicationConfigPo.getRobotId());
        }
        return applicationInfo;
    }


    private ApplicationInfoDTO getAidaApplicationInfo(String applicationId, String robotId) {
        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        InnerAppConfigDTO innerAppConfigDTO = aidaInvokeServiceProxy.getAidaRobotInfo(applicationId, robotId);
        if (innerAppConfigDTO != null) {
            applicationInfoDTO.setApplicationType(AbilityEnum.mapAidaRobotType(innerAppConfigDTO.getAppType()));
            applicationInfoDTO.setApplicationParamList(convertParamList(innerAppConfigDTO.getUserInputFormList()));
        }
        return applicationInfoDTO;
    }

    private List<ApplicationInfoDTO.Param> convertParamList(List<InnerAppConfigDTO.UserInputForm> userInputFormList) {
        if (CollectionUtils.isEmpty(userInputFormList)) {
            return Collections.emptyList();
        }
        List<ApplicationInfoDTO.Param> paramList = new ArrayList<>();
        for (InnerAppConfigDTO.UserInputForm userInputForm : userInputFormList) {
            if (userInputForm.getText() != null) {
                ApplicationInfoDTO.Param param = new ApplicationInfoDTO.Param();
                param.setFieldCode(userInputForm.getText().getVariable());
                param.setFieldName(userInputForm.getText().getLabel());
                param.setIsRequire(userInputForm.getText().getRequired());
                paramList.add(param);
            }
            if (userInputForm.getSelect() != null) {
                ApplicationInfoDTO.Param param = new ApplicationInfoDTO.Param();
                param.setFieldCode(userInputForm.getSelect().getVariable());
                param.setFieldName(userInputForm.getSelect().getLabel());
                param.setIsRequire(userInputForm.getSelect().getRequired());
                paramList.add(param);
            }
            if (userInputForm.getPhoto() != null) {
                ApplicationInfoDTO.Param param = new ApplicationInfoDTO.Param();
                param.setFieldCode(userInputForm.getPhoto().getVariable());
                param.setFieldName(userInputForm.getPhoto().getLabel());
                param.setIsRequire(userInputForm.getPhoto().getRequired());
                paramList.add(param);
            }
            if (userInputForm.getParagraph() != null) {
                ApplicationInfoDTO.Param param = new ApplicationInfoDTO.Param();
                param.setFieldCode(userInputForm.getParagraph().getVariable());
                param.setFieldName(userInputForm.getParagraph().getLabel());
                param.setIsRequire(userInputForm.getParagraph().getRequired());
                paramList.add(param);
            }
        }
        return paramList;
    }

    private ApplicationInfoDTO getPbApplicationInfo(String robotId) {
        ApplicationInfoDTO applicationInfoDTO = new ApplicationInfoDTO();
        applicationInfoDTO.setApplicationType(AbilityEnum.MULTI_ROUND.getCode());
        applicationInfoDTO.setApplicationParamList(convertSlotInfoToParamList(pbServiceProxy.getSlotInfoListById(Long.valueOf(robotId))));
        return applicationInfoDTO;
    }

    private List<ApplicationInfoDTO.Param> convertSlotInfoToParamList(List<SlotInfoDTO> slotInfoDTOList) {
        if (CollectionUtils.isEmpty(slotInfoDTOList)) {
            return Collections.emptyList();
        }
        return slotInfoDTOList.stream().map(this::convertSlotInfoToParam).collect(Collectors.toList());
    }

    private ApplicationInfoDTO.Param convertSlotInfoToParam(SlotInfoDTO slotInfoDTO) {
        ApplicationInfoDTO.Param param = new ApplicationInfoDTO.Param();
        param.setFieldName(slotInfoDTO.getSlotDesc());
        param.setFieldCode(slotInfoDTO.getSlotName());
        param.setIsRequire(Boolean.FALSE);
        return param;
    }

    private PageData<ApplicationDTO> convertApplicationPageInfo(int totalNum, int pageNum, int pageSize, List<ApplicationConfigPo> pageDataList) {
        List<ApplicationDTO> applicationList = pageDataList.stream().map(applicationConfigPo -> {
            ApplicationDTO applicationDTO = new ApplicationDTO();
            applicationDTO.setId(applicationConfigPo.getId());
            applicationDTO.setName(applicationConfigPo.getName());
            applicationDTO.setDescription(applicationConfigPo.getDescription());
            applicationDTO.setCreateTime(DateUtil.format(applicationConfigPo.getGmtCreated(), null));
            applicationDTO.setUpdateTime(DateUtil.format(applicationConfigPo.getGmtModified(), null));
            applicationDTO.setCreateMis(applicationConfigPo.getCreatorMis());
            return applicationDTO;
        }).collect(Collectors.toList());
        return PageData.create(totalNum, pageNum, pageSize, applicationList);
    }

    /**
     * 生成数据集模版文件名
     * 格式：【模板】-{identity}.xlsx
     *
     * @param param 数据集应用参数
     * @return 文件名
     * @see ApplicationExecuteServiceImpl#generateIdentity
     */
    private String generateDatasetFileName(DatasetApplicationParam param) {
        String fileNameTemplate = "【模板】-%s.xlsx";
        String identity = generateIdentity(param);
        return FileNameUtil.processFileName(String.format(fileNameTemplate, identity));
    }

    /**
     * 生成文件名标识<br>
     * 单个文件为 {应用名称}-{版本名称}<br>
     * 多个文件为 {Hash} 应用和版本计算SHA-256, HEX字符串取前16位
     *
     * @param param 数据集应用参数
     * @return 文件名标识
     */
    private String generateIdentity(DatasetApplicationParam param) {
        List<String> applicationNameList = getApplicationNameList(param);

        if (applicationNameList.size() == 1) {
            return generateSingleApplicationIdentity(param, applicationNameList.get(0));
        } else {
            return generateMultipleApplicationIdentity(applicationNameList);
        }
    }

    /**
     * 获取应用名称列表
     *
     * @param param 数据集应用参数
     * @return 应用名称列表
     */
    private List<String> getApplicationNameList(DatasetApplicationParam param) {
        if (param.getApplicationSource() == TaskModelSourceEnum.APPLICATION.getCode()) {
            List<ApplicationConfigPo> applicationConfigPoList = applicationConfigGeneratorService.getByIdList(param.getApplicationIdList());
            return applicationConfigPoList.stream()
                    .map(ApplicationConfigPo::getName)
                    .filter(Objects::nonNull)  // 过滤掉空值
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
        } else {
            return param.getAidaModelConfigList().stream()
                    .map(AidaModelConfig::getApplicationName)
                    .filter(Objects::nonNull)  // 过滤掉空值
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());
        }
    }


    /**
     * 生成单个应用的文件名标识
     *
     * @param param           数据集应用参数
     * @param applicationName 应用名称
     * @return 文件名标识
     */
    private String generateSingleApplicationIdentity(DatasetApplicationParam param, String applicationName) {
        if (param.getApplicationSource() != TaskModelSourceEnum.APPLICATION.getCode()) {
            AidaModelConfig aidaModelConfig = param.getAidaModelConfigList().get(0);
            String modelConfigVersionName = aidaModelConfig.getModelConfigVersionName();
            return extractAppName(applicationName, modelConfigVersionName) + "-" + modelConfigVersionName;
        } else {
            return applicationName;
        }
    }

    /**
     * 生成多个应用的文件名标识
     *
     * @param applicationNameList 应用名称列表
     * @return 文件名标识
     */
    private String generateMultipleApplicationIdentity(List<String> applicationNameList) {
        return DateUtil.getNow();
    }

    /**
     * 提取应用名称，不包含版本信息
     * 版本信息通过 " - " 分隔
     *
     * @param fullAppName            完整的应用名称
     * @param modelConfigVersionName 模型配置版本名称
     * @return 提取后的应用名称
     */
    private String extractAppName(String fullAppName, String modelConfigVersionName) {
        if (fullAppName == null) {
            return "";
        }
        String separator = " - ";
        int index = fullAppName.lastIndexOf(separator + modelConfigVersionName);
        return index != -1 ? fullAppName.substring(0, index) : fullAppName;
    }

}
