package com.meituan.csc.aigc.eval.service.push;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.csc.aigc.eval.enums.MessageTypeEnum;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.xm.pubapi.thrift.GroupPushMessageWithUids;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import com.sankuai.xm.pubapi.thrift.PusherInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class DxPushTextService {

    @Autowired
    private PushMessageServiceI.Iface pushMessageService;

    @Value("${dx.appKey}")
    private String AppKey;

    private String Token;

    @Value("${dx.targetUrl}")
    private String TargetUrl;

    @Value("${dx.uid}")
    private Long Uid;

    private PusherInfo pusherInfo;


    public DxPushTextService() {
        try {
            Token = Kms.getByName("com.sankuai.csccratos.eval.service", "eval-dx-token");
        } catch (KmsResultNullException e) {
            //处理异常
            log.error("DXPushService.Token initialize error", e);
        }
    }

    @PostConstruct
    private void initPusherInfo() {
        pusherInfo = new PusherInfo(AppKey, Token, Uid.longValue(), (short) 0, (short) 0, (short) 0, "");
    }

    public JSONObject pushTextByMisName(String text, String... receivers) {
        JSONObject result = null;
        long timeStamp = System.currentTimeMillis();
        List<String> rec = Arrays.asList(receivers);
        JSONObject info = new JSONObject();
        info.put("appkey", pusherInfo.appkey);
        info.put("token", pusherInfo.token);
        info.put("fromUid", pusherInfo.fromUid);
        info.put("toAppid", pusherInfo.toAppid);
        info.put("appId", pusherInfo.appId);
        info.put("channelId", pusherInfo.channelId);
        info.put("fromName", pusherInfo.fromName);
        log.info("DXPushService thrift pushTextByMisName api parameter:timeStamp: {},text: {},receivers: {}," +
                "PusherInfo: {}", timeStamp, text, rec, info);
        int retryTimes = 3;
        Boolean successFlag = false;
        while (retryTimes > 0 && !successFlag) {
            try {
                String result0 = pushMessageService.pushTextMessage(timeStamp, text, rec, pusherInfo);
                log.info("DXPushService thrift pushTextByMisName result:{}", result0);
                result = JSONObject.parseObject(result0);
                if (result != null && result.getIntValue("rescode") == 0) {
                    successFlag = true;
                }
            } catch (Exception e) {
                log.error("DXPushService thrift pushTextByMisName error", e);
            } finally {
                retryTimes--;
            }
        }
        if (result == null || result.getIntValue("rescode") != 0) {
            log.warn("DXPushService pushTextByMisName wrong, result:{}", result);
        }

        return result;
    }

    public boolean pushDirectResultTextByMisName(String text, String receiver) {
        try {
            JSONObject result;
            result = pushTextByMisName(text, receiver);
            if (result == null || result.getIntValue("rescode") != 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("pushDirectResultTextByMisName error,text:{},receiver:{},e:", text, receiver, e);
            return false;
        }
    }

    /**
     * 发送特定类型大象消息
     *
     * @param pusherInfo
     * @param messageType
     * @param bodyJson
     * @param receivers
     * @return
     * @throws Exception
     */
    public JSONObject pushMessageByMisName(PusherInfo pusherInfo, MessageTypeEnum messageType, Object bodyJson, String... receivers) throws Exception {
        JSONObject result;
        long timeStamp = System.currentTimeMillis();
        List<String> rec = Arrays.asList(receivers);
        String result0 = pushMessageService.pushMessage(timeStamp, messageType.getType(), JSONObject.toJSONString(bodyJson), rec, pusherInfo);
        result = JSONObject.parseObject(result0);
        log.info("DXPushService thrift pushMessageByMisName result:{}", result0);

        if (result == null || result.getIntValue("rescode") != 0) {
            log.warn("DXPushService pushMessageByMisName wrong, result:{}", result);
        }

        return result;
    }

    /**
     * 发送特定类型大象消息
     *
     * @param pusherInfo
     * @param messageType
     * @param bodyJson
     * @param receivers
     * @return
     * @throws Exception
     */
    public boolean pushDirectResultMessageByMisName(PusherInfo pusherInfo, MessageTypeEnum messageType, Object bodyJson, String receivers) {
        try {
            JSONObject result;
            result = pushMessageByMisName(pusherInfo, messageType, bodyJson, receivers);
            if (result == null || result.getIntValue("rescode") != 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("pushDirectResultMessageByMisName error,messageType:{},bodyJson:{},receiver:{},e:", messageType, bodyJson, receivers, e);
            return false;
        }
    }

    /**
     * 发送大象群组消息
     */
    public void pushMsgToGroup(String msg, Long groupId) throws Exception {
        GroupPushMessageWithUids uids = new GroupPushMessageWithUids();
        uids.setCts(System.currentTimeMillis());
        uids.setMessageType("text");
        uids.setMessageBodyJson(msg);
        uids.setGid(groupId);
        uids.setPusherInfo(pusherInfo);
        System.out.println(JSON.toJSONString(uids));
        String result0 = pushMessageService.pushToRoomWithUids(uids);
        System.out.println(result0);


    }
}