package com.meituan.csc.aigc.eval.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.meituan.csc.aigc.eval.config.Module;
import com.meituan.csc.aigc.eval.constants.CommonConstants;
import com.meituan.csc.aigc.eval.constants.LionConstants;
import com.meituan.csc.aigc.eval.dao.entity.*;
import com.meituan.csc.aigc.eval.dao.service.generator.*;
import com.meituan.csc.aigc.eval.dto.*;
import com.meituan.csc.aigc.eval.enums.*;
import com.meituan.csc.aigc.eval.exception.CheckException;
import com.meituan.csc.aigc.eval.exception.EvalException;
import com.meituan.csc.aigc.eval.param.ConversationInfo;
import com.meituan.csc.aigc.eval.param.gpt.AidaRequest;
import com.meituan.csc.aigc.eval.param.gpt.GptRequest;
import com.meituan.csc.aigc.eval.param.gpt.GptResponse;
import com.meituan.csc.aigc.eval.param.task.TaskQuerySessionConditionParam;
import com.meituan.csc.aigc.eval.proxy.AidaInvokeServiceProxy;
import com.meituan.csc.aigc.eval.proxy.GptRequestServiceProxy;
import com.meituan.csc.aigc.eval.service.ArenaTaskService;
import com.meituan.csc.aigc.eval.utils.CommonUtils;
import com.meituan.csc.aigc.eval.utils.RandomUtil;
import com.meituan.csc.aigc.eval.utils.ThreadUtils;
import com.meituan.csc.aigc.runtime.dto.aida.AidaFinalRes;
import com.meituan.inf.xmdlog.ConfigUtil;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ArenaTaskServiceImpl implements ArenaTaskService {
    @Autowired
    private EvalTaskGeneratorService evalTaskGeneratorService;
    @Autowired
    private EvalTaskQueryGeneratorService evalTaskQueryGeneratorService;
    @Autowired
    private EvalTaskQueryDetailGeneratorService evalTaskQueryDetailGeneratorService;
    @Autowired
    private AidaInvokeServiceProxy aidaInvokeServiceProxy;
    @Autowired
    private GptRequestServiceProxy gptRequestServiceProxy;
    @Autowired
    private EvalTaskSessionGeneratorService evalTaskSessionGeneratorService;

    //竞技模型顺序
    private static final int PLAYER_A = 1;
    private static final int PLAYER_B = 2;
    //空字符串
    private static final String EMPTY = "";
    //竞技任务指标数量
    private static final Integer ARENA_METRIC_SIZE = 1;
    private static final ExecutorService TEMPLATE_THREAD_POOL = ThreadUtils.createPoolWithTraceAndCat(40, 200, 1000, "execute-arena-send-message-%d");

    // 实现接口中的方法


    @Override
    public ArenaSessionDTO beginNewSession(Long taskId) throws EvalException {
        ArenaSessionDTO arenaSessionDTO = new ArenaSessionDTO();
        EvalTaskPo taskPo = getEvalTaskPo(taskId);
        //获取当前任务下同一session未结束的评测（单轮：一条query；多轮：未评测query+所关联的历史query）
        List<EvalTaskQueryPo> filteredEvalTaskQueryPos = new ArrayList<>();
        if (AbilityEnum.SINGLE_ROUND.equals(AbilityEnum.parse(taskPo.getAbility()))) {
            filteredEvalTaskQueryPos = evalTaskQueryGeneratorService.getByTaskId(taskId).stream()
                    .filter(po -> TaskQueryStatusEnum.ARENA_EVALUATING.equals(TaskQueryStatusEnum.parse(po.getStatus())))
                    .collect(Collectors.toList());
        } else {
            List<EvalTaskQueryPo> evalTaskQueryPos = evalTaskQueryGeneratorService.getByTaskId(taskId);
            if (CollectionUtils.isNotEmpty(evalTaskQueryPos)) {
                AtomicReference<String> conversationId = new AtomicReference<>();
                //按照conversationId聚合
                Map<String, List<EvalTaskQueryPo>> conversationEvalTaskQueryMap = evalTaskQueryPos.stream().collect(Collectors.groupingBy(EvalTaskQueryPo::getConversationId));
                //获取历史query
                for (Map.Entry<String, List<EvalTaskQueryPo>> conversationQueryMap : conversationEvalTaskQueryMap.entrySet()) {
                    List<EvalTaskQueryPo> completedQuery = conversationQueryMap.getValue().stream()
                            .filter(po -> TaskQueryStatusEnum.COMPLETED.equals(TaskQueryStatusEnum.parse(po.getStatus())))
                            .collect(Collectors.toList());
                    List<EvalTaskQueryPo> arenaQuery = conversationQueryMap.getValue().stream()
                            .filter(po -> TaskQueryStatusEnum.ARENA_EVALUATING.equals(TaskQueryStatusEnum.parse(po.getStatus())))
                            .collect(Collectors.toList());
                    if ((CollectionUtils.isNotEmpty(completedQuery) || CollectionUtils.isNotEmpty(arenaQuery)) && completedQuery.size() < conversationQueryMap.getValue().size()) {
                        conversationId.set(conversationQueryMap.getKey());
                        break;
                    }
                }
                if (conversationId.get() != null) {
                    filteredEvalTaskQueryPos = evalTaskQueryPos.stream()
                            .filter(po -> conversationId.get().equals(po.getConversationId()))
                            .collect(Collectors.toList());
                }
            }
        }
        arenaSessionDTO = createArenaSessionDTO(taskPo, filteredEvalTaskQueryPos);
        if (CollectionUtils.isNotEmpty(filteredEvalTaskQueryPos)) {
            arenaSessionDTO.setSessionId(filteredEvalTaskQueryPos.get(0).getConversationId());
        } else if (EMPTY.equals(taskPo.getDatasetIds())) {
            String sessionId = UUID.randomUUID().toString();
            EvalTaskSessionPo evalTaskSessionPo = createTaskSession(arenaSessionDTO.getTaskId(), 0L, sessionId);
            arenaSessionDTO.setSessionId(String.valueOf(evalTaskSessionPo.getId()));
        } else {
            Long datasetId = getDatasetId(taskPo, null);
            if (datasetId == null) {
                throw new CheckException("评测集已全部评完");
            }
            EvalTaskQueryPo evalTaskQueryPo = getDatasetDetail(taskPo, datasetId, null);
            if (evalTaskQueryPo == null) {
                throw new CheckException("随机query发生错误");
            }
            arenaSessionDTO.setSessionId(evalTaskQueryPo.getConversationId());
        }
        return arenaSessionDTO;
    }

    private EvalTaskPo getEvalTaskPo(Long taskId) {
        EvalTaskPo taskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(taskPo != null && !taskPo.getIsDeleted(), "任务不存在或已经删除");
        taskPo.setStatus(AutoTaskStatusEnum.EVALUATING.getCode());
        evalTaskGeneratorService.updateById(taskPo);
        return taskPo;
    }

    private ArenaSessionDTO createArenaSessionDTO(EvalTaskPo taskPo, List<EvalTaskQueryPo> filteredEvalTaskQueryPos) {
        ArenaSessionDTO arenaSessionDTO = new ArenaSessionDTO();
        arenaSessionDTO.setTaskId(taskPo.getId());
        arenaSessionDTO.setTaskName(taskPo.getName());
        arenaSessionDTO.setTaskCreator(taskPo.getCreatorMis());
        arenaSessionDTO.setDatasets(getDatasetIdList(taskPo));
        if (filteredEvalTaskQueryPos.isEmpty()) {
            arenaSessionDTO.setHistory(new ArrayList<>());
            arenaSessionDTO.setVoted(true);
        } else {
            //重新进入竞技页面后判断历史数据中是否有未投票的对话
            AtomicBoolean isVoted = new AtomicBoolean(true);
            Map<Long, EvalTaskQueryPo> evalTaskQueryMap = filteredEvalTaskQueryPos.stream()
                    .collect(Collectors.toMap(EvalTaskQueryPo::getId, po -> po));
            filteredEvalTaskQueryPos.forEach(evalTaskQueryPo -> {
                if (TaskQueryStatusEnum.ARENA_EVALUATING.equals(TaskQueryStatusEnum.parse(evalTaskQueryPo.getStatus()))) {
                    isVoted.set(false);
                }
            });
            Set<Long> queryIdSet = filteredEvalTaskQueryPos.stream().map(EvalTaskQueryPo::getId).collect(Collectors.toSet());
            List<EvalTaskQueryDetailPo> evalTaskQueryDetailPos = evalTaskQueryDetailGeneratorService.getByTaskId(taskPo.getId());
            //需要展示的queryDetail数据
            List<EvalTaskQueryDetailPo> filterEvalTaskQueryDetailPos = evalTaskQueryDetailPos.stream()
                    .filter(po -> queryIdSet.contains(po.getQueryId()))
                    .sorted(Comparator.comparing(EvalTaskQueryDetailPo::getGmtCreated))
                    .collect(Collectors.toList());
            //根据被测模型分组
            Map<String, List<EvalTaskQueryDetailPo>> modelQueryDetailPos = filterEvalTaskQueryDetailPos.stream()
                    .collect(Collectors.groupingBy(EvalTaskQueryDetailPo::getTestModel));
            List<ArenaSessionDTO.ArenaModelHistoryData> historyDataList = new ArrayList<>();
            for (Map.Entry<String, List<EvalTaskQueryDetailPo>> modelQueryDetailPo : modelQueryDetailPos.entrySet()) {
                ArenaSessionDTO.ArenaModelHistoryData history = new ArenaSessionDTO.ArenaModelHistoryData();
                history.setModelName(modelQueryDetailPo.getKey());
                List<CommonMessage> messageList = new ArrayList<>();
                // 按照时间顺序返回历史信息
                modelQueryDetailPo.getValue().forEach(queryDetail -> {
                    CommonMessage systemMessage = new CommonMessage();
                    systemMessage.setContent(queryDetail.getModelOutput());
                    systemMessage.setRole(CommonConstants.GPT_PARAM_SYSTEM);
                    systemMessage.setTime(queryDetail.getGmtCreated().getTime());
                    systemMessage.setQueryId(queryDetail.getQueryId());
                    CommonMessage userMessage = new CommonMessage();
                    EvalTaskQueryPo evalTaskQueryPo = evalTaskQueryMap.get(queryDetail.getQueryId());
                    userMessage.setContent(evalTaskQueryPo.getInput());
                    userMessage.setRole(CommonConstants.GPT_PARAM_USER);
                    userMessage.setTime(evalTaskQueryPo.getGmtCreated().getTime());
                    userMessage.setQueryId(evalTaskQueryPo.getId());
                    messageList.add(userMessage);
                    messageList.add(systemMessage);
                });
                history.setMessageList(messageList);
                historyDataList.add(history);
            }
            //如果是多轮无评测集的竞技任务，如果没有未投票的query，“新会话”就可以开启新的session
            if (isVoted.get() && TaskTypeEnum.ARENA.equals(TaskTypeEnum.parse(taskPo.getType())) && EMPTY.equals(taskPo.getDatasetIds())) {
                arenaSessionDTO.setHistory(new ArrayList<>());
            } else {
                arenaSessionDTO.setHistory(historyDataList);
            }
            arenaSessionDTO.setVoted(isVoted.get());
        }
        arenaSessionDTO.setModelList(getModelList(taskPo));
        return arenaSessionDTO;
    }

    private List<Long> getDatasetIdList(EvalTaskPo taskPo) {
        if (EMPTY.equals(taskPo.getDatasetIds())) {
            return new ArrayList<>();
        } else {
            return Arrays.stream(taskPo.getDatasetIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
    }

    private List<ArenaModelDTO> getModelList(EvalTaskPo taskPo) {
        List<String> selectedModelList = Arrays.stream(taskPo.getTestModel().split(",")).collect(Collectors.toList());
        List<ArenaModelDTO> modelDTOS = Lists.newArrayList();
        modelDTOS.add(new ArenaModelDTO(selectedModelList.get(0), PLAYER_A));
        modelDTOS.add(new ArenaModelDTO(selectedModelList.get(1), PLAYER_B));
        return modelDTOS;
    }

    @Override
    public ArenaSendMessageDTO sendMessage(ArenaSendMessageParam sendMessageParam) throws Exception {
        //参数校验
        checkParam(sendMessageParam);
        //无数据集的条件下先将内容存储到query表中
        if (null == sendMessageParam.getQueryId()) {
            EvalTaskQueryPo evalTaskQueryPo = createEvalTaskQueryPo(sendMessageParam.getTaskId(), sendMessageParam.getDatasetId(), sendMessageParam.getSessionId(), sendMessageParam.getQuery());
            evalTaskQueryGeneratorService.save(evalTaskQueryPo);
            sendMessageParam.setQueryId(evalTaskQueryPo.getId());
            changeTaskSessionStatus(Long.valueOf(sendMessageParam.getSessionId()), TaskSessionStatusEnum.EVALUATING);
        }
        ArenaSendMessageDTO sendMessageDTO = new ArenaSendMessageDTO();
        final CountDownLatch latch = new CountDownLatch(2);
        Map<String, ArenaSendMessageDTO.ModelResult> modelMap = new ConcurrentHashMap<>();
        Map<String, String> taskSessionConversationMap = new ConcurrentHashMap<>();
        for (int i = 0; i < 2; i++) {
            int finalIndex = i;
            TEMPLATE_THREAD_POOL.submit(() -> {
                try {
                    doExecute(latch, sendMessageParam, finalIndex, modelMap, taskSessionConversationMap);
                } catch (Exception e) {
                    log.error(Module.EVAL_ARENA_TASK + "-sendMessage.doExecute, error{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
        }
        // 强制走主库，避免主从延迟
        ZebraForceMasterHelper.forceMasterInLocalContext();
        //修改query状态为竞技评测中
        EvalTaskQueryPo evalTaskQueryPo = evalTaskQueryGeneratorService.getById(sendMessageParam.getQueryId());
        if (null != evalTaskQueryPo && !TaskQueryStatusEnum.ARENA_EVALUATING.equals(TaskQueryStatusEnum.parse(evalTaskQueryPo.getStatus()))) {
            evalTaskQueryPo.setStatus(TaskQueryStatusEnum.ARENA_EVALUATING.getCode());
            evalTaskQueryPo.setGmtModified(new Date());
            evalTaskQueryGeneratorService.updateById(evalTaskQueryPo);
        }
        ZebraForceMasterHelper.clearLocalContext();
        // 主线程等待所有子线程一定时间, 超时提醒用户稍后刷新页面
        Integer expireTime = Lion.getInt(ConfigUtil.getAppkey(), LionConstants.ARENA_MODEL_OUTPUT_EXPIRE_TIME, 18000);
        boolean isSuccess = latch.await(expireTime, TimeUnit.MILLISECONDS);
        //修改task_session表
        if (!taskSessionConversationMap.isEmpty()) {
            EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(sendMessageParam.getSessionId());
            evalTaskSessionPo.setConversationId(JSON.toJSONString(taskSessionConversationMap));
            evalTaskSessionPo.setGmtModified(new Date());
            evalTaskSessionGeneratorService.updateById(evalTaskSessionPo);
        }
        if (!isSuccess) {
            throw new RuntimeException("等待模型回复，请稍后刷新页面");
        }
        sendMessageDTO.setModelResult(new ArrayList<>(modelMap.values()));
        sendMessageDTO.setQueryId(sendMessageParam.getQueryId());
        return sendMessageDTO;
    }

    private void doExecute(CountDownLatch latch, ArenaSendMessageParam sendMessageParam, int index, Map<String, ArenaSendMessageDTO.ModelResult> modelMap, Map<String, String> taskSessionConversationMap) throws Exception {
        // 执行任务
        try {
            String model = sendMessageParam.getModelList().get(index);
            EvalTaskPo taskPo = evalTaskGeneratorService.getById(sendMessageParam.getTaskId());
            //获取Aida配置
            Map<String, String> aidaConfig = JSON.parseObject(taskPo.getExtra(), new TypeReference<Map<String, String>>() {
            });
            String mis = getUserMis();
            ArenaSessionMessageDTO sessionMessageDTO = new ArenaSessionMessageDTO();
            List<String> glmModelList = Lion.getList(ConfigUtil.getAppkey(), "aida.python.model.list", String.class);
            String modelOutput;
            String totalToken;
            Integer executeTime = null;
            if (StringUtils.isNotBlank(model) && CollectionUtils.isNotEmpty(glmModelList) && glmModelList.contains(model)) {
                GptRequest gptRequest = buildGptRequest(model, sendMessageParam, aidaConfig, taskSessionConversationMap);
                GptResponse gptResponse = gptRequestServiceProxy.invokeGlm(gptRequest);
                modelOutput = gptResponse.getGptReply();
                totalToken = String.valueOf(gptResponse.getTotalToken());
                if (gptResponse.getCostTime() != null) {
                    executeTime = gptResponse.getCostTime().intValue();
                }
            } else {
                // 创建一个计时器对象，用于记录获取aida机器人输出的时间。
                Stopwatch stopwatch = Stopwatch.createStarted();
                //发起调用
                AidaRequest aidaRequest = buildAidaRequest(model, sendMessageParam, mis, aidaConfig, taskSessionConversationMap);
                AidaFinalRes aidaFinalRes = aidaInvokeServiceProxy.getAidaResult(aidaRequest);
                stopwatch.stop();
                if (aidaFinalRes == null || !"200".equals(aidaFinalRes.getCode())) {
                    log.error(Module.EVAL_ARENA_TASK + "-sendMessage, data:{}, error:{}", JSON.toJSONString(sessionMessageDTO), "调用aida异常");
                    throw new Exception("调用aida异常");
                }
                modelOutput = aidaFinalRes.getAnswer();
                totalToken = aidaFinalRes.getUsage() == null ? EMPTY : String.valueOf(aidaFinalRes.getUsage().getTotal_tokens());
                executeTime = (int) stopwatch.elapsed(TimeUnit.MILLISECONDS);
            }
            ArenaSendMessageDTO.ModelResult modelResult = new ArenaSendMessageDTO.ModelResult();
            modelResult.setModelName(model);
            modelResult.setRole(CommonConstants.GPT_PARAM_SYSTEM);
            modelResult.setContent(modelOutput);
            modelResult.setTime(System.currentTimeMillis());
            modelResult.setCost(executeTime);
            modelResult.setToken(totalToken);
            modelMap.put(model, modelResult);
            saveEvalTaskQueryDetailPo(sendMessageParam, modelResult);
        } finally {
            latch.countDown();
        }
    }

    private EvalTaskQueryPo createEvalTaskQueryPo(Long taskId, Long datasetId, String sessionId, String query) {
        EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(sessionId);
        EvalTaskQueryPo evalTaskQueryPo = new EvalTaskQueryPo();
        evalTaskQueryPo.setTaskId(taskId);
        evalTaskQueryPo.setDatasetId(datasetId);
        evalTaskQueryPo.setSessionId(evalTaskSessionPo.getSessionId());
        evalTaskQueryPo.setConversationId(sessionId);
        evalTaskQueryPo.setInput(query);
        Date now = new Date();
        evalTaskQueryPo.setGmtCreated(now);
        evalTaskQueryPo.setGmtModified(now);
        evalTaskQueryPo.setStatus(TaskQueryStatusEnum.EVALUATING.getCode());
        return evalTaskQueryPo;
    }

    private GptRequest buildGptRequest(String modelName, ArenaSendMessageParam sendMessageParam, Map<String, String> aidaConfig, Map<String, String> taskSessionConversationMap) {
        EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(sendMessageParam.getSessionId());
        Map<String, String> conversationMap = new HashMap<>();
        if (StringUtils.isNotEmpty(evalTaskSessionPo.getConversationId())) {
            conversationMap = JSON.parseObject(evalTaskSessionPo.getConversationId(), new TypeReference<Map<String, String>>() {
            });
        }
        GptRequest gptRequest = new GptRequest();
        gptRequest.setPrompt(aidaConfig.get(modelName));
        if (conversationMap.containsKey(modelName)) {
            gptRequest.setSessionId(conversationMap.get(modelName));
        } else {
            String uuid = UUID.randomUUID().toString();
            taskSessionConversationMap.put(modelName, uuid);
            gptRequest.setSessionId(uuid);
        }
        gptRequest.setModelName(modelName);
        gptRequest.setInputContent(sendMessageParam.getQuery());
        return gptRequest;
    }

    private AidaRequest buildAidaRequest(String modelName, ArenaSendMessageParam sendMessageParam, String mis, Map<String, String> aidaConfig, Map<String, String> taskSessionConversationMap) {
        CommonUtils.checkEval(aidaConfig.get(modelName) != null, "模型配置不存在");
        EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(sendMessageParam.getSessionId());
        Map<String, String> conversationMap = new HashMap<>();
        if (StringUtils.isNotEmpty(evalTaskSessionPo.getConversationId())) {
            conversationMap = JSON.parseObject(evalTaskSessionPo.getConversationId(), new TypeReference<Map<String, String>>() {
            });
        }
        JSON.parseObject(evalTaskSessionPo.getConversationId(), new TypeReference<Map<String, String>>() {
        });
        AidaRequest aidaRequest = new AidaRequest();
        aidaRequest.setParams(new HashMap<>());
        aidaRequest.setUser(mis);
        aidaRequest.setInputContent(sendMessageParam.getQuery());
        aidaRequest.setApiSecretKey(Lion.getString(ConfigUtil.getAppkey(), "ai.aida.save.secret", "app-6GvWK2h9VHYu8cVT06JMNIc1"));
        aidaRequest.setFridayKey(aidaInvokeServiceProxy.getDefaultFridayKey());
        aidaRequest.setModelConfigVersionId(aidaConfig.get(modelName));
        if (conversationMap.containsKey(modelName)) {
            aidaRequest.setConversationId(conversationMap.get(modelName));
        } else {
            ConversationInfo conversationInfo = aidaInvokeServiceProxy.generateAidaConversationId(aidaRequest);
            taskSessionConversationMap.put(modelName, conversationInfo.getConversationId());
            aidaRequest.setConversationId(conversationInfo.getConversationId());
        }
        return aidaRequest;
    }


    private void saveEvalTaskQueryDetailPo(ArenaSendMessageParam sendMessageParam, ArenaSendMessageDTO.ModelResult modelResult) {
        EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(sendMessageParam.getSessionId());
        Date now = new Date();
        EvalTaskQueryDetailPo evalTaskQueryDetailPo = new EvalTaskQueryDetailPo();
        evalTaskQueryDetailPo.setTaskId(sendMessageParam.getTaskId());
        evalTaskQueryDetailPo.setDatasetId(sendMessageParam.getDatasetId());
        evalTaskQueryDetailPo.setSessionId(evalTaskSessionPo.getSessionId());
        evalTaskQueryDetailPo.setQueryId(sendMessageParam.getQueryId());
        evalTaskQueryDetailPo.setTestModel(modelResult.getModelName());
        evalTaskQueryDetailPo.setModelOutput(modelResult.getContent());
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(sendMessageParam.getTaskId());
        //解析竞技任务的指标
        evalTaskQueryDetailPo.setMetricId(parseMetric(evalTaskPo));
        evalTaskQueryDetailPo.setStatus(TaskQueryDetailStatusEnum.INCOMPLETE.getCode());
        evalTaskQueryDetailPo.setGmtCreated(now);
        evalTaskQueryDetailPo.setGmtModified(now);
        evalTaskQueryDetailPo.setExecuteTime(modelResult.getCost());
        evalTaskQueryDetailPo.setCostToken(modelResult.getToken());
        evalTaskQueryDetailGeneratorService.save(evalTaskQueryDetailPo);
    }

    private Integer parseMetric(EvalTaskPo evalTaskPo) {
        String metric = evalTaskPo.getMetrics();
        if (StringUtils.isBlank(metric)) {
            throw new CheckException("任务指标为空");
        }
        List<Integer> metricList = Arrays.stream(metric.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if (metricList.size() > ARENA_METRIC_SIZE) {
            throw new CheckException("任务指标数量冗余");
        }
        return metricList.get(0);
    }


    @Override
    public ArenaSessionGenerateMessageDTO generateMsg(String conversationId, Long taskId) throws Exception {
        ArenaSessionGenerateMessageDTO generateMessageDTO = new ArenaSessionGenerateMessageDTO();
        EvalTaskPo taskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(taskPo != null && !taskPo.getIsDeleted(), "任务不存在或已经删除");
        Long datasetId = getDatasetId(taskPo, conversationId);
        if (datasetId == null) {
            throw new CheckException("评测集已全部评完");
        }
        EvalTaskQueryPo evalTaskQueryPo = getDatasetDetail(taskPo, datasetId, conversationId);
        if (evalTaskQueryPo == null) {
            throw new Exception("随机query发生错误");
        }
        generateMessageDTO.setTaskId(taskId);
        generateMessageDTO.setSessionId(conversationId);
        generateMessageDTO.setDatasetId(datasetId);
        generateMessageDTO.setContent(evalTaskQueryPo.getInput());
        generateMessageDTO.setQueryId(evalTaskQueryPo.getId());
        return generateMessageDTO;
    }

    @Override
    public ArenaSessionDTO vote(ArenaSessionVoteParam voteParam) throws Exception {
        ArenaSessionDTO arenaSessionDTO = new ArenaSessionDTO();
        String sessionId = voteParam.getSessionId();
        ArenaVoteResultEnum voteResultEnum = ArenaVoteResultEnum.parse(voteParam.getVoteType());
        if (isInvalidVoteParam(sessionId, voteResultEnum)) {
            throw new CheckException("无效的投票参数");
        }
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(voteParam.getTaskId());
        updateEvalTaskQueryPos(voteParam);
        updateEvalTaskQueryDetailPos(voteParam, voteResultEnum);
        updateEvalTaskSessionPo(voteParam, voteResultEnum);
        if (AbilityEnum.SINGLE_ROUND.equals(AbilityEnum.parse(evalTaskPo.getAbility()))) {
            //无数据集情况
            if (EMPTY.equals(evalTaskPo.getDatasetIds())) {
                String uuid = UUID.randomUUID().toString();
                EvalTaskSessionPo evalTaskSessionPo = createTaskSession(voteParam.getTaskId(), 0L, uuid);
                arenaSessionDTO.setSessionId(String.valueOf(evalTaskSessionPo.getId()));
            } else {
                //有数据集情况
                Long datasetId = getDatasetId(evalTaskPo, null);
                if (datasetId == null) {
                    arenaSessionDTO.setVoted(true);
                    arenaSessionDTO.setSessionId(UUID.randomUUID().toString());
                    return arenaSessionDTO;
                }
                EvalTaskQueryPo evalTaskQueryPo = getDatasetDetail(evalTaskPo, datasetId, null);
                if (evalTaskQueryPo == null) {
                    arenaSessionDTO.setVoted(true);
                    arenaSessionDTO.setSessionId(UUID.randomUUID().toString());
                    return arenaSessionDTO;
                }
                arenaSessionDTO.setSessionId(evalTaskQueryPo.getConversationId());
            }
            changeTaskSessionStatus(Long.valueOf(voteParam.getSessionId()), TaskSessionStatusEnum.COMPLETED);
        } else {
            //无数据集情况
            if (EMPTY.equals(evalTaskPo.getDatasetIds())) {
                arenaSessionDTO.setSessionId(voteParam.getSessionId());
            } else {
                //有数据集情况
                getMultiRoundSessionId(voteParam, arenaSessionDTO);
            }
        }
        arenaSessionDTO.setVoted(true);
        log.info(Module.EVAL_ARENA_TASK + "-vote, 投票返回值:{}", JSON.toJSONString(arenaSessionDTO));
        return arenaSessionDTO;
    }

    private boolean isInvalidVoteParam(String sessionId, ArenaVoteResultEnum voteResultEnum) {
        return StringUtils.isEmpty(sessionId) || voteResultEnum == null;
    }

    private void updateEvalTaskQueryPos(ArenaSessionVoteParam voteParam) {
        // 强制走主库，避免主从延迟
        ZebraForceMasterHelper.forceMasterInLocalContext();
        EvalTaskQueryPo evalTaskQueryPo = evalTaskQueryGeneratorService.getById(voteParam.getQueryId());
        Date now = new Date();
        evalTaskQueryPo.setStatus(TaskQueryStatusEnum.COMPLETED.getCode());
        evalTaskQueryPo.setGmtModified(now);
        evalTaskQueryGeneratorService.updateById(evalTaskQueryPo);
        ZebraForceMasterHelper.clearLocalContext();
    }

    private void updateEvalTaskQueryDetailPos(ArenaSessionVoteParam voteParam, ArenaVoteResultEnum voteResultEnum) {
        List<EvalTaskQueryDetailPo> evalTaskQueryDetailPos = evalTaskQueryDetailGeneratorService.getByQueryIds(Collections.singletonList(voteParam.getQueryId()));
        Date now = new Date();
        for (EvalTaskQueryDetailPo queryDetailPo : evalTaskQueryDetailPos) {
            queryDetailPo.setMetricResult(String.valueOf(voteResultEnum.getCode()));
            queryDetailPo.setStatus(TaskQueryDetailStatusEnum.COMPLETED.getCode());
            queryDetailPo.setGmtModified(now);
        }
        evalTaskQueryDetailGeneratorService.saveOrUpdateBatch(evalTaskQueryDetailPos);
    }

    private void updateEvalTaskSessionPo(ArenaSessionVoteParam voteParam, ArenaVoteResultEnum voteResultEnum) {
        EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(voteParam.getSessionId());
        evalTaskSessionPo.setSessionResult(String.valueOf(voteResultEnum.getCode()));
        evalTaskSessionPo.setGmtModified(new Date());
        evalTaskSessionGeneratorService.updateById(evalTaskSessionPo);
    }

    private void changeTaskSessionStatus(Long id, TaskSessionStatusEnum statusEnum) {
        EvalTaskSessionPo evalTaskSessionPo = evalTaskSessionGeneratorService.getById(id);
        evalTaskSessionPo.setStatus(statusEnum.getCode());
        evalTaskSessionGeneratorService.updateById(evalTaskSessionPo);
    }

    private void getMultiRoundSessionId(ArenaSessionVoteParam voteParam, ArenaSessionDTO arenaSessionDTO) throws Exception {
        EvalTaskQueryPo evalTaskQueryPo = evalTaskQueryGeneratorService.getById(voteParam.getQueryId());
        List<EvalTaskQueryPo> evalTaskQueryPoList = evalTaskQueryGeneratorService.getByTaskAndDataset(evalTaskQueryPo.getTaskId(), evalTaskQueryPo.getDatasetId());
        //查看{datasetId}下是否还有未评测的query
        List<EvalTaskQueryPo> filterDatasetId = evalTaskQueryPoList.stream()
                .filter(po -> TaskQueryStatusEnum.EVALUATING.equals(TaskQueryStatusEnum.parse(po.getStatus())))
                .collect(Collectors.toList());
        //查看{datasetId, sessionId}下是否还有未评测的query
        List<EvalTaskQueryPo> filterDataSetIdAndSessionId = filterDatasetId.stream()
                .filter(po -> po.getConversationId().equals(voteParam.getSessionId()))
                .collect(Collectors.toList());
        EvalTaskPo evalTaskPo = evalTaskGeneratorService.getById(voteParam.getTaskId());
        if (!CollectionUtils.isEmpty(filterDataSetIdAndSessionId)) {
            arenaSessionDTO.setSessionId(voteParam.getSessionId());
        } else if (!CollectionUtils.isEmpty(filterDatasetId)) {
            EvalTaskQueryPo randomQuery = RandomUtil.getRandomElement(filterDatasetId);
            arenaSessionDTO.setSessionId(randomQuery.getConversationId());
            changeTaskSessionStatus(Long.valueOf(voteParam.getSessionId()), TaskSessionStatusEnum.COMPLETED);
        } else {
            Long newDatasetId = getDatasetId(evalTaskPo, null);
            if (newDatasetId == null) {
                //评测集全部评完
                arenaSessionDTO.setSessionId(UUID.randomUUID().toString());
            } else {
                List<EvalTaskQueryPo> newEvalTaskQueryPoList = evalTaskQueryGeneratorService.getByTaskAndDataset(evalTaskQueryPo.getTaskId(), newDatasetId);
                // 查看新的{datasetId}下是否还有未评测的query
                EvalTaskQueryPo randomQuery = RandomUtil.getRandomElement(newEvalTaskQueryPoList);
                arenaSessionDTO.setSessionId(randomQuery.getConversationId());
            }
            changeTaskSessionStatus(Long.valueOf(voteParam.getSessionId()), TaskSessionStatusEnum.COMPLETED);
        }
    }


    @Override
    public ArenaTaskCompleteDTO completeTask(Long taskId) throws EvalException {
        ArenaTaskCompleteDTO arenaTaskCompleteDTO = new ArenaTaskCompleteDTO();
        EvalTaskPo taskPo = evalTaskGeneratorService.getById(taskId);
        CommonUtils.checkEval(taskPo != null && !taskPo.getIsDeleted(), "任务不存在或已经删除");
        taskPo.setStatus(AutoTaskStatusEnum.FINISHED.getCode());
        taskPo.setGmtModified(new Date());
        taskPo.setUpdaterMis(getUserMis());
        evalTaskGeneratorService.updateById(taskPo);
        arenaTaskCompleteDTO.setIsSuccess(true);
        return arenaTaskCompleteDTO;
    }

    private void checkParam(ArenaSendMessageParam sendMessageParam) throws CheckException {
        CommonUtils.checkEval(sendMessageParam != null, "参数为空");
        CommonUtils.checkEval(!EMPTY.equals(sendMessageParam.getQuery()), "用户问题不能为空");
        CommonUtils.checkEval(sendMessageParam.getTaskId() != null, "任务ID不能为空");
        CommonUtils.checkEval(sendMessageParam.getSessionId() != null, "会话ID不能为空");
        CommonUtils.checkEval(sendMessageParam.getDatasetId() != null, "数据集id不能为空");
    }


    private String getUserMis() {
        User user = UserUtils.getUser();
        return user != null ? user.getLogin() : "huhaiyang06";
    }

    private Long getDatasetId(EvalTaskPo evalTaskPo, String conversationId) {
        List<EvalTaskQueryPo> evalTaskQueryPoList = evalTaskQueryGeneratorService.getByTaskId(evalTaskPo.getId());
        Map<Long, List<EvalTaskQueryPo>> datasetTaskQueryMap = evalTaskQueryPoList.stream().collect(Collectors.groupingBy(EvalTaskQueryPo::getDatasetId));
        List<Long> filterDatasetIdList = new ArrayList<>();
        for (Map.Entry<Long, List<EvalTaskQueryPo>> entry : datasetTaskQueryMap.entrySet()) {
            List<EvalTaskQueryPo> filterEvalTaskQueryPoList = entry.getValue().stream()
                    .filter(e -> TaskQueryStatusEnum.EVALUATING.equals(TaskQueryStatusEnum.parse(e.getStatus())))
                    .collect(Collectors.toList());
            for (EvalTaskQueryPo query : filterEvalTaskQueryPoList) {
                if (query.getConversationId().equals(conversationId)) {
                    return query.getDatasetId();
                }
            }
            if (!filterEvalTaskQueryPoList.isEmpty()) {
                filterDatasetIdList.add(entry.getKey());
            }
        }
        if (CollectionUtils.isEmpty(filterDatasetIdList)) {
            return null;
        }
        List<Long> selectedList = RandomUtil.getRandomSubList(filterDatasetIdList, 1);
        return selectedList.size() == 1 ? selectedList.get(0) : null;
    }

    private EvalTaskQueryPo getDatasetDetail(EvalTaskPo taskPo, Long datasetId, String conversationId) {
        List<EvalTaskQueryPo> evalTaskQueryPoList = evalTaskQueryGeneratorService.getByTaskId(taskPo.getId());
        //选取datasetId下评测中的query
        List<EvalTaskQueryPo> filterDatasetEvalTaskQueryPoList = evalTaskQueryPoList.stream()
                .filter(queryPo -> queryPo.getDatasetId().equals(datasetId) && TaskQueryStatusEnum.EVALUATING.equals(TaskQueryStatusEnum.parse(queryPo.getStatus())))
                .collect(Collectors.toList());
        if (AbilityEnum.SINGLE_ROUND.equals(AbilityEnum.parse(taskPo.getAbility()))) {
            //单轮随机返回
            return RandomUtil.getRandomElement(filterDatasetEvalTaskQueryPoList);
        } else {
            //多轮按conversationId返回，并且按照turn字段升序
            List<EvalTaskQueryPo> filterSessionEvalTaskQueryPoList = filterDatasetEvalTaskQueryPoList.stream()
                    .filter(queryPo -> queryPo.getConversationId().equals(conversationId))
                    .sorted(Comparator.comparing(EvalTaskQueryPo::getTurn))
                    .collect(Collectors.toList());
            //多轮按顺序返回
            if (filterSessionEvalTaskQueryPoList.isEmpty()) {
                Map<String, List<EvalTaskQueryPo>> sessionQueryMap = filterDatasetEvalTaskQueryPoList.stream().collect(Collectors.groupingBy(EvalTaskQueryPo::getSessionId));
                List<String> sessionList = new ArrayList<>(sessionQueryMap.keySet());
                String randomSessionId = RandomUtil.getRandomElement(sessionList);
                filterSessionEvalTaskQueryPoList = sessionQueryMap.get(randomSessionId).stream()
                        .sorted(Comparator.comparing(EvalTaskQueryPo::getTurn))
                        .collect(Collectors.toList());
            }
            return filterSessionEvalTaskQueryPoList.get(0);
        }
    }

    //若task_session表不存在该记录，则创建
    private EvalTaskSessionPo createTaskSession(Long taskId, Long datasetId, String sessionId) {
        TaskQuerySessionConditionParam condition = new TaskQuerySessionConditionParam();
        condition.setTaskId(taskId);
        condition.setDatasetId(datasetId);
        condition.setSessionId(sessionId);
        List<EvalTaskSessionPo> evalTaskSessionPos = evalTaskSessionGeneratorService.getByCondition(condition);
        if (CollectionUtils.isEmpty(evalTaskSessionPos)) {
            EvalTaskSessionPo taskSessionPo = new EvalTaskSessionPo();
            taskSessionPo.setTaskId(taskId);
            taskSessionPo.setDatasetId(datasetId);
            taskSessionPo.setSessionId(sessionId);
            Date now = new Date();
            taskSessionPo.setGmtCreated(now);
            taskSessionPo.setGmtModified(now);
            taskSessionPo.setStatus(TaskSessionStatusEnum.EVALUATING.getCode());
            evalTaskSessionGeneratorService.save(taskSessionPo);
            return taskSessionPo;
        }
        return evalTaskSessionPos.get(0);
    }
}

