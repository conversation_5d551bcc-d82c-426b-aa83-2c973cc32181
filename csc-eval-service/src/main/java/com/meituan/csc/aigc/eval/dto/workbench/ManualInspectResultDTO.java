package com.meituan.csc.aigc.eval.dto.workbench;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ManualInspectResultDTO implements Serializable {
    /**
     * 质检指标结果ID
     */
    private Long inspectInfoId;

    /**
     * 指标ID
     */
    private Long metricId;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 指标类型
     */
    private Integer metricType;

    /**
     * 质检结果
     */
    private List<String> result;

    /**
     * 结果备注
     */
    private String resultNote;

    /**
     * 质检时间
     */
    private String time;

    /**
     * 质检人
     */
    private String mis;

    /**
     * 人工质检结果枚举选项
     */
    private List<String> resultEnum;

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * 外部业务消息id
     */
    private String messageId;

    /**
     * aida消息id
     */
    private String llmMessageId;

    /**
     * aida应用id
     */
    private String platformApp;

    /**
     * aida空间id
     */
    private String platformWorkspace;
}
