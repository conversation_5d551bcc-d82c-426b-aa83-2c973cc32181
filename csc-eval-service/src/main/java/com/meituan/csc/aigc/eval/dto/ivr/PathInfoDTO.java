package com.meituan.csc.aigc.eval.dto.ivr;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 路径信息DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PathInfoDTO implements Serializable {

    private static final long serialVersionUID = -751869194835614843L;

    /**
     * 流程访问ID
     */
    private String flowVisitId;

    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 模块访问ID
     */
    private String moduleVisitId;

    /**
     * 模块ID
     */
    private String moduleId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 跳转ID
     */
    private String gotoId;

    /**
     * 版本类型
     */
    private Integer versionType;

    /**
     * 是否新流程
     */
    private Boolean isNewFlow;

    /**
     * 流程属性
     */
    private Integer flowAttr;


    /**
     * 模块类型code
     */
    private Integer moduleTypeCode;

    /**
     * 模块类型名称
     */
    private String moduleTypeName;

    /**
     * 分支线上的值
     */
    private String branchName;

    /**
     * 入参列表
     */
    private List<ParamDTO> inputParams;

    /**
     * 出参列表
     */
    private List<ParamDTO> outputParams;

    /**
     * 参数DTO
     */
    @Data
    public static class ParamDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 参数编码
         */
        private String code;

        /**
         * 参数名称
         */
        private String name;

        /**
         * 参数值
         */
        private String value;

        /**
         * 参数类型
         */
        private String type;

        /**
         * 属性名称
         */
        private String propertyName;

        /**
         * 参数类型码
         */
        private Integer paramType;

        /**
         * 表格数据
         */
        private Object tableData;

        /**
         * 参数名
         */
        private String paramName;

        /**
         * 参数值
         */
        private String paramValue;
    }
}
