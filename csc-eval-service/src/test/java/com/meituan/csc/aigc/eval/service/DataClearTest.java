package com.meituan.csc.aigc.eval.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.meituan.csc.aigc.eval.Application;
import com.meituan.csc.aigc.eval.dao.service.generator.AidaMessagesGeneratorService;
import com.meituan.csc.aigc.eval.excel.listener.SortedExcelCommonListener;
import com.meituan.csc.aigc.eval.param.workbench.AidaMessagesConditionParam;
import com.meituan.csc.aigc.eval.param.workbench.AidaMessagesSessionGroupResultParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = {Application.class})
@Ignore
public class DataClearTest {

    static String aaa = "{\"result1\":\"%s\"}";

//    public static void main(String[] args) {
//        String a = "fraud_auto_recognition\\?serviceId=(\\d+)&caseId=(\\d+)";
//        SortedExcelCommonListener listener = new SortedExcelCommonListener();
//        EasyExcelFactory.read("/Users/<USER>/Desktop/到客骗赔识别-结果2.xlsx", listener).sheet(0).doRead();
//        List<TestVO> testVOList = new ArrayList<>();
//        for (Map<String, String> data : listener.getExcelDataList()) {
//            TestVO testVO = new TestVO();
//            Pattern pattern = Pattern.compile(a);
//            Matcher matcher = pattern.matcher(data.get("输入内容"));
//            if (matcher.matches()) {
//                testVO.setSessionId(matcher.group(1));
//                testVO.setCaseId(matcher.group(2));
//            }
//
//            JSONObject jsonObject = JSONObject.parseObject(data.get("模型输出"));
////            testVO.setInput(data.get("对话历史"));
//            String output=jsonObject.getString("result").equals("骗赔")?"是":"否";
//            testVO.setOutput(output);
//            testVOList.add(testVO);
//        }
//        EasyExcelFactory.write("/Users/<USER>/Desktop/到客骗赔识别-结果3.xlsx", TestVO.class).sheet(0).doWrite(testVOList);
//    }

    public static void main(String[] args) {
        SortedExcelCommonListener listener = new SortedExcelCommonListener();
        EasyExcelFactory.read("/Users/<USER>/Desktop/评测结果-不限时间2.xlsx", listener).sheet(0).doRead();
        Map<String, Integer> errorMap = new HashMap<>();
        Map<String, Integer> successMap = new HashMap<>();
        for (Map<String, String> data : listener.getExcelDataList()) {
            JSONObject jsonObject = JSONObject.parseObject(data.get("模型输出"));
            if (data.get("是否符合预期").equals("否")) {
                errorMap.put(jsonObject.getString("info"), errorMap.getOrDefault(jsonObject.getString("info"), 0) + 1);
            } else {
                successMap.put(jsonObject.getString("info"), successMap.getOrDefault(jsonObject.getString("info"), 0) + 1);
            }
        }
        System.out.println(errorMap);
        System.out.println(successMap);

//        SortedExcelCommonListener listener = new SortedExcelCommonListener();
//        EasyExcelFactory.read("/Users/<USER>/Desktop/评测结果.xlsx", listener).sheet(0).doRead();
//        List<TestVO> testVOList = new ArrayList<>();
//        for (Map<String, String> data : listener.getExcelDataList()) {
//            TestVO testVO = new TestVO();
//            String input=data.get("输入内容");
//            Pattern pattern = Pattern.compile("fraud_auto_recognition\\?serviceId=(\\d+)&caseId=(\\d+)");
//            Matcher matcher = pattern.matcher(input);
//            if (matcher.matches()) {
//                testVO.setSessionId(matcher.group(1));
//                testVO.setCaseId(matcher.group(2));
//            }
//            String modelOutput=data.get("模型输出");
//            JSONObject jsonObject = JSONObject.parseObject(modelOutput);
//            testVO.setOutput(jsonObject.getString("result").equals("骗赔")?"是" : "否");
//            testVO.setRealOutput(data.get("预期结果"));
//            testVO.setModelOutput(modelOutput);
//            testVO.setEval_score(testVO.getOutput().equals(testVO.getRealOutput())?"是":"否");
//            testVOList.add(testVO);
//        }
//        EasyExcelFactory.write("/Users/<USER>/Desktop/评测结果2.xlsx", TestVO.class).sheet(0).doWrite(testVOList);
    }


//    public static void main(String[] args) {
//        SortedExcelCommonListener listener = new SortedExcelCommonListener();
//        EasyExcelFactory.read("/Users/<USER>/Desktop/到客骗赔识别-结果.xlsx", listener).sheet(0).doRead();
//
//        SortedExcelCommonListener listener2 = new SortedExcelCommonListener();
//        EasyExcelFactory.read("/Users/<USER>/Desktop/实际配置.xlsx", listener2).sheet(0).doRead();
//        List<TestVO> testVOList = new ArrayList<>();
//        Map<String, List<String>> session2Config = new HashMap<>();
//        for (Map<String, String> data : listener2.getExcelDataList()) {
//            List<String> config = session2Config.getOrDefault(data.get("工单id"), new ArrayList<>());
//            if (CollectionUtils.isEmpty(config) || !data.get("实际是否骗赔").equals(config.get(0))) {
//                config.add(data.get("实际是否骗赔"));
//            }
//            session2Config.put(data.get("工单id"), config);
//        }
//        Map<String,List<String>>  copy=new HashMap<>();
//        for (Map.Entry<String,List<String>> entry: session2Config.entrySet()){
//            if(entry.getValue().size()>1){
//                session2Config.remove(entry.getKey());
//                copy.put(entry.getKey(),entry.getValue());
//            }
//        }
//        for (Map<String, String> data : listener.getExcelDataList()) {
//            TestVO testVO = new TestVO();
////            testVO.setInput(data.get("对话历史"));
//            testVO.setCaseId(data.get("caseId"));
//            testVO.setSessionId(data.get("sessionId"));
//            testVO.setOutput(data.get("output"));
//            testVO.setRealOutput(session2Config.get(data.get("caseId")).get(0));
//            testVOList.add(testVO);
//        }
//        System.out.println(JSONObject.toJSONString(copy));
//        EasyExcelFactory.write("/Users/<USER>/Desktop/到客骗赔识别-结果2.xlsx", TestVO.class).sheet(0).doWrite(testVOList);
//    }

//
//    @Test
//    @Ignore
//    public void testWaihu() {
//        String inputFileName = "20240122少收钱-端到端-待标注-20240130";
//        String outputFileName = inputFileName + new Date();
//        String inputPath = String.format("%s%s%s", "/Users/<USER>/Downloads/", inputFileName, ".xlsx");
//        String outputPath = String.format("%s%s%s", "/Users/<USER>/Downloads/", outputFileName, ".xlsx");
//
//        List<TestVO> testVOList = readExcelData(inputPath);
//        EasyExcelFactory.write(outputPath, TestVO.class).sheet(0).doWrite(testVOList);
//    }
//
//    private List<TestVO> readExcelData(String inputPath) {
//        SortedExcelCommonListener listener = new SortedExcelCommonListener();
//        EasyExcelFactory.read(inputPath, listener).sheet(0).doRead();
//        List<TestVO> testVOList = new ArrayList<>();
//        Map<String, String> session2HistoryMap = Maps.newHashMap();
//
//        for (Map<String, String> data : listener.getExcelDataList()) {
//            TestVO testVO = createTestVO(data, session2HistoryMap);
//            testVOList.add(testVO);
//        }
//        return testVOList;
//    }
//
//    private TestVO createTestVO(Map<String, String> data, Map<String, String> session2HistoryMap) {
//        TestVO testVO = new TestVO();
//        String sessionId = data.get("contactId");
//        testVO.setSessionId(sessionId);
//        String userQuery = data.get("用户语料");
//        String modelOutput = data.get("机器人实际回复");
//        testVO.setInput("用户:" +userQuery);
//        updateSessionHistory(sessionId, userQuery, session2HistoryMap, testVO);
//        Map<String, String> params = createParams(data, sessionId, modelOutput, session2HistoryMap);
//        testVO.setParams(JSON.toJSONString(params));
//        return testVO;
//    }
//
//    private void updateSessionHistory(String sessionId, String userQuery, Map<String, String> session2HistoryMap, TestVO testVO) {
//        if (session2HistoryMap.containsKey(sessionId)) {
//            String history = session2HistoryMap.get(sessionId);
//            history += "\n用户:" +userQuery;
//            session2HistoryMap.put(sessionId, history);
//            testVO.setInput(history);
//        } else {
//            session2HistoryMap.put(sessionId, testVO.getInput());
//        }
//    }
//
//    private Map<String, String> createParams(Map<String, String> data, String sessionId, String modelOutput, Map<String, String> session2HistoryMap) {
//        Map<String, String> params = new HashMap<>();
//        params.put("轮次", data.get("轮次"));
//        params.put("会话评级", data.get("会话评级"));
//        params.put("标注人", data.get("标注人"));
//        params.put("用户意向", data.get("用户意向"));
//        params.put("最终意向", data.get("最终意向"));
//        params.put("__OUTPUT__", modelOutput);
//        updateModelOutputHistory(sessionId, modelOutput, session2HistoryMap);
//        params.put("话术正确性得分", data.get("话术正确性得分"));
//        return params;
//    }
//
//    private void updateModelOutputHistory(String sessionId, String modelOutput, Map<String, String> session2HistoryMap) {
//        if (session2HistoryMap.containsKey(sessionId)) {
//            String history = session2HistoryMap.get(sessionId);
//            history += "\n系统:" +modelOutput;
//            session2HistoryMap.put(sessionId, history);
//        }
//    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TestVO implements Serializable {
        /**
         * 输入内容
         */
        @ExcelProperty(value = "工单ID", index = 0)
        private String caseId;

        /**
         * 输入内容
         */
        @ExcelProperty(value = "会话ID", index = 1)
        private String sessionId;

        /**
         * 变量
         */
        @ExcelProperty(value = "模型判断是否骗赔", index = 2)
        private String output;

        /**
         * 预期输出
         */
        @ExcelProperty(value = "预期结果", index = 3)
        private String realOutput;

        @ExcelProperty(value = "模型输出", index = 4)
        private String modelOutput;

        @ExcelProperty(value = "是否符合预期", index = 5)
        private String eval_score;
    }
}
