package com.meituan.csc.aigc.eval.service.impl;

import com.meituan.csc.aigc.eval.dao.entity.EvalTaskPo;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalDatasetDetailGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalTaskGeneratorService;
import com.meituan.csc.aigc.eval.dao.service.generator.EvalTaskQueryGeneratorService;
import com.meituan.csc.aigc.eval.param.task.AreaEvalRequest;
import com.meituan.csc.aigc.eval.param.task.EvalRequest;
import com.meituan.csc.aigc.eval.service.strategy.eval.impl.ArenaEvalStrategyServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ArenaEvalStrategyServiceImplTest {

    @InjectMocks
    private ArenaEvalStrategyServiceImpl arenaEvalStrategyService;

    @Mock
    private EvalTaskGeneratorService evalTaskGeneratorService;

    @Mock
    private EvalDatasetDetailGeneratorService evalDatasetDetailGeneratorService;

    @Mock
    private EvalTaskQueryGeneratorService evalTaskQueryGeneratorService;

    /**
     * 测试execute方法，当输入的评测请求包含数据集ID时
     */
    @Test
    public void testExecuteWithDatasetId() throws Throwable {
        // arrange
        AreaEvalRequest evalRequest = new AreaEvalRequest();
        evalRequest.setDataSetIdList(Arrays.asList(1L, 2L, 3L));
        EvalTaskPo evalTaskPo = new EvalTaskPo();
        when(evalTaskGeneratorService.save(any(EvalTaskPo.class))).thenReturn(Boolean.TRUE);

        // act
        arenaEvalStrategyService.execute(evalRequest);

        // assert
        verify(evalTaskGeneratorService, times(1)).save(any(EvalTaskPo.class));
        verify(evalDatasetDetailGeneratorService, times(3)).getByDatasetId(anyLong());
    }

    /**
     * 测试execute方法，当输入的评测请求不包含数据集ID时
     */
    @Test
    public void testExecuteWithoutDatasetId() throws Throwable {
        // arrange
        AreaEvalRequest evalRequest = new AreaEvalRequest();
        EvalTaskPo evalTaskPo = new EvalTaskPo();
        when(evalTaskGeneratorService.save(any(EvalTaskPo.class))).thenReturn(Boolean.TRUE);

        // act
        arenaEvalStrategyService.execute(evalRequest);

        // assert
        verify(evalTaskGeneratorService, times(1)).save(any(EvalTaskPo.class));
        verify(evalDatasetDetailGeneratorService, never()).getByDatasetId(anyLong());
    }

    /**
     * 测试execute方法，当输入的评测请求为null时
     */
    @Test(expected = NullPointerException.class)
    public void testExecuteWithNullRequest() throws Throwable {
        // arrange
        AreaEvalRequest evalRequest = null;

        // act
        arenaEvalStrategyService.execute(evalRequest);

        // assert
        // Exception is expected, no need for further assertions
    }

    /**
     * 测试execute方法，当保存任务时抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testExecuteWithExceptionOnSaveTask() throws Throwable {
        // arrange
        AreaEvalRequest evalRequest = new AreaEvalRequest();
        when(evalTaskGeneratorService.save(any(EvalTaskPo.class))).thenThrow(new RuntimeException());

        // act
        arenaEvalStrategyService.execute(evalRequest);

        // assert
        // Exception is expected, no need for further assertions
    }

    /**
     * 测试execute方法，当保存查询时抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testExecuteWithExceptionOnSaveQueries() throws Throwable {
        // arrange
        AreaEvalRequest evalRequest = new AreaEvalRequest();
        evalRequest.setDataSetIdList(Arrays.asList(1L));
        EvalTaskPo evalTaskPo = new EvalTaskPo();
        when(evalTaskGeneratorService.save(any(EvalTaskPo.class))).thenReturn(Boolean.TRUE);
        when(evalDatasetDetailGeneratorService.getByDatasetId(anyLong())).thenThrow(new RuntimeException());

        // act
        arenaEvalStrategyService.execute(evalRequest);

        // assert
        // Exception is expected, no need for further assertions
    }
}