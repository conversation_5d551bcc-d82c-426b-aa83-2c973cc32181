package com.meituan.csc.aigc.eval.service.call.out;

import com.alibaba.fastjson.JSON;
import com.meituan.csc.aigc.eval.Application;
import com.meituan.csc.aigc.eval.proxy.RecordQueryServiceProxy;
import com.sankuai.call.sdk.entity.record.QueryRecordDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest(classes = {Application.class})
@RunWith(SpringRunner.class)
@Slf4j
public class CallTest {

    @Resource
    public RecordQueryServiceProxy recordQueryServiceProxy;

    @Test
    public void testRecordQuertService() {
        List<QueryRecordDataDTO> response = recordQueryServiceProxy.queryDualRecordData("20200710150840754341-125594");
        System.out.println(JSON.toJSONString(response, true));
    }
}
